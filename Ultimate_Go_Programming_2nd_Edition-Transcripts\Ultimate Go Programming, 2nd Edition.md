#  Ultimate Go Programming Course

##  Course Chapter Videos

| Video                                            | Duration |
| ------------------------------------------------ | -------- |
| **Language Syntax**                              |          |
| 2.1 Topics                                       | 0:00:48  |
| 2.2 Variables                                    | 0:16:26  |
| 2.3 Struct Types                                 | 0:23:27  |
| 2.4 Pointers Part 1 (Pass by Value)              | 0:15:45  |
| 2.5 Pointer Part 2 (Sharing Data)                | 0:10:35  |
| 2.6 Pointers Part 3 (Escape Analysis)            | 0:20:20  |
| 2.7 Pointers Part 4 (Stack Growth)               | 0:07:32  |
| 2.8 Pointers Part 5 (Garbage Collection)         | 0:15:13  |
| 2.9 Constants                                    | 0:15:29  |
| **Data Structures**                              |          |
| 3.1 Topics                                       | 0:00:41  |
| 3.2 Data-Oriented Design                         | 0:04:52  |
| 3.3 Arrays Part 1 (Mechanical Sympathy)          | 0:33:10  |
| 3.4 Arrays Part 2 (Semantics)                    | 0:16:43  |
| 3.5 Slices Part 1 (Declare and Length)           | 0:08:46  |
| 3.6 Slices Part 2 (Appending Slices)             | 0:15:32  |
| 3.7 Slices Part 3 (Taking Slices of Slices)      | 0:11:45  |
| 3.8 Slices Part 4 (Slices and References)        | 0:05:51  |
| 3.9 Slices Part 5 (Strings and Slices)           | 0:08:29  |
| 3.10 Slices Part 6 (Range Mechanics)             | 0:04:35  |
| 3.11 Maps                                        | 0:08:03  |
| **Decoupling**                                   |          |
| 4.1 Topics                                       | 0:00:56  |
| 4.2 Methods Part 1 (Declare & Receiver Behavior) | 0:10:45  |
| 4.3 Methods Part 2 (Value & Pointer Semantics)   | 0:15:35  |
| 4.4 Methods Part 3 (Function Method Variables)   | 0:13:40  |
| 4.5 Interfaces Part 1 (Polymorphism)             | 0:20:11  |
| 4.6 Interfaces Part 2 (Method Sets)              | 0:11:51  |
| 4.7 Interfaces Part 3 (Storage by Value)         | 0:05:34  |
| 4.8 Embedding                                    | 0:07:30  |
| 4.9 Exporting                                    | 0:08:29  |
| **Composition**                                  |          |
| 5.1 Topics                                       | 0:00:59  |
| 5.2 Grouping Types                               | 0:12:38  |
| 5.3 Decoupling Part 1                            | 0:06:58  |
| 5.4 Decoupling Part 2                            | 0:18:25  |
| 5.5 Decoupling Part 3                            | 0:14:36  |
| 5.6 Conversion and Assertions                    | 0:09:02  |
| 5.7 Interface Pollution                          | 0:06:45  |
| 5.8 Mocking                                      | 0:05:53  |
| 5.9 Design Guidelines                            | 0:03:25  |
| **Error Handling**                               |          |
| 6.1 Topics                                       | 0:00:51  |
| 6.2 Default Error Values                         | 0:11:33  |
| 6.3 Error Variables                              | 0:02:40  |
| 6.4 Type as Context                              | 0:07:04  |
| 6.5 Behavior as Context                          | 0:09:50  |
| 6.6 Find the Bug                                 | 0:08:52  |
| 6.7 Wrapping Errors                              | 0:14:30  |
| **Packaging**                                    |          |
| 7.1 Topics                                       | 0:00:52  |
| 7.2 Language Mechanics                           | 0:08:32  |
| 7.3 Design Guidelines                            | 0:05:49  |
| 7.4 Package-Oriented Design                      | 0:18:26  |
| **Goroutines**                                   |          |
| 8.1 Topics                                       | 0:00:29  |
| 8.2 OS Scheduler Mechanics                       | 0:28:59  |
| 8.3 Go Scheduler Mechanics                       | 0:20:41  |
| 8.4 Creating Goroutines                          | 0:19:43  |
| **Data Races**                                   |          |
| 9.1 Topics                                       | 0:00:53  |
| 9.2 Cache Coherency and False Sharing            | 0:12:39  |
| 9.3 Synchronization with Atomic Functions        | 0:11:30  |
| 9.4 Synchronization with Mutexes                 | 0:14:38  |
| 9.5 Race Detection                               | 0:04:48  |
| 9.6 Map Data Race                                | 0:04:01  |
| 9.7 Interface-Based Race Condition               | 0:08:14  |
| **Channels**                                     |          |
| 10.1 Topics                                      | 0:00:43  |
| 10.2 Signaling Semantics                         | 0:17:50  |
| 10.3 Basic Patterns Part 1                       | 0:11:12  |
| 10.4 Basic Patterns Part 2                       | 0:04:19  |
| 10.5 Basic Patterns Part 3                       | 0:05:59  |
| 10.6 Pooling Pattern                             | 0:06:23  |
| 10.7 Fan Out Pattern Part 1                      | 0:08:37  |
| 10.8 Fan Out Pattern Part 2                      | 0:06:24  |
| 10.9 Drop Pattern                                | 0:07:14  |
| 10.10 Cancellation Pattern                       | 0:08:15  |
| **Concurrency Patterns**                         |          |
| 11.1 Topics                                      | 0:00:34  |
| 11.2 Context Part 1                              | 0:16:23  |
| 11.3 Context Part 2                              | 0:11:24  |
| 11.4 Failure Detection                           | 0:23:17  |
| **Testing**                                      |          |
| 12.1 Topics                                      | 0:00:41  |
| 12.2 Basic Unit Testing                          | 0:13:54  |
| 12.3 Table Unit Testing                          | 0:03:19  |
| 12.4 Mocking Web Server Response                 | 0:06:59  |
| 12.5 Testing Internal Endpoints                  | 0:07:22  |
| 12.6 Example Tests                               | 0:09:55  |
| 12.7 Sub Tests                                   | 0:05:35  |
| 12.8 Code Coverage                               | 0:04:44  |
| **Benchmarking**                                 |          |
| 13.1 Topics                                      | 0:00:46  |
| 13.2 Basic Benchmarking                          | 0:07:26  |
| 13.3 Sub Benchmarks                              | 0:03:35  |
| 13.4 Validate Benchmarks                         | 0:07:41  |
| **Profiling and Tracing**                        |          |
| 14.1 Topics                                      | 0:00:55  |
| 14.2 Profiling Guidelines                        | 0:10:48  |
| 14.3 Stack Traces                                | 0:09:00  |
| 14.4 Micro Level Optimization                    | 0:31:17  |
| 14.5 Macro Level Opt. - GODEBUG Tracing          | 0:12:49  |
| 14.6 Macro Level Opt. - Memory Profiling         | 0:16:07  |
| 14.7 Macro Level Opt. - Tooling Changes          | 0:06:03  |
| 14.8 Macro Level Opt. - CPU Profiling            | 0:05:53  |
| 14.9 Execution Tracing                           | 0:34:24  |
| **Summary**                                      |          |
| Ultimate Go Programming Summary                  | 0:01:11  |
