// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package cli

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	C "github.com/urfave/cli/v2"
)

func identityHKT(typeA string) string {
	return typeA
}

func generateIdentityTraverseTuple(f *os.File, i int) {
	generateTraverseTuple1(identityHKT, "")(f, i)
}

func generateIdentitySequenceTuple(f *os.File, i int) {
	generateSequenceTuple1(identityHKT, "")(f, i)
}

func generateIdentitySequenceT(f *os.File, i int) {
	generateSequenceT1(identityHKT, "")(f, i)
}

func generateIdentityHelpers(filename string, count int) error {
	dir, err := os.Getwd()
	if err != nil {
		return err
	}
	absDir, err := filepath.Abs(dir)
	if err != nil {
		return err
	}
	pkg := filepath.Base(absDir)
	f, err := os.Create(filepath.Clean(filename))
	if err != nil {
		return err
	}
	defer f.Close()
	// log
	log.Printf("Generating code in [%s] for package [%s] with [%d] repetitions ...", filename, pkg, count)

	// some header
	fmt.Fprintln(f, "// Code generated by go generate; DO NOT EDIT.")
	fmt.Fprintln(f, "// This file was generated by robots at")
	fmt.Fprintf(f, "// %s\n\n", time.Now())

	fmt.Fprintf(f, "package %s\n\n", pkg)

	fmt.Fprintf(f, `
import (
	A "github.com/IBM/fp-go/internal/apply"
	T "github.com/IBM/fp-go/tuple"
)
`)

	for i := 1; i <= count; i++ {
		// sequenceT
		generateIdentitySequenceT(f, i)
		// sequenceTuple
		generateIdentitySequenceTuple(f, i)
		// traverseTuple
		generateIdentityTraverseTuple(f, i)
	}

	return nil
}

func IdentityCommand() *C.Command {
	return &C.Command{
		Name:  "identity",
		Usage: "generate code for Identity",
		Flags: []C.Flag{
			flagCount,
			flagFilename,
		},
		Action: func(ctx *C.Context) error {
			return generateIdentityHelpers(
				ctx.String(keyFilename),
				ctx.Int(keyCount),
			)
		},
	}
}
