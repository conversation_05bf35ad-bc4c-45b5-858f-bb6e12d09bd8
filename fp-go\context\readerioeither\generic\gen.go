package generic

// Code generated by go generate; DO NOT EDIT.
// This file was generated by robots at
// 2024-05-24 22:24:01.4250895 +0200 CEST m=+0.014515801

import (
	"context"

	E "github.com/IBM/fp-go/either"
	A "github.com/IBM/fp-go/internal/apply"
	RE "github.com/IBM/fp-go/readerioeither/generic"
	T "github.com/IBM/fp-go/tuple"
)

// Eitherize0 converts a function with 0 parameters returning a tuple into a function with 0 parameters returning a [GRA]
// The inverse function is [Uneitherize0]
func Eitherize0[GRA ~func(context.Context) GIOA, F ~func(context.Context) (R, error), GIOA ~func() E.Either[error, R], R any](f F) func() GRA {
	return RE.Eitherize0[GRA](f)
}

// Uneitherize0 converts a function with 0 parameters returning a [GRA] into a function with 0 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize0[GRA ~func(context.Context) GIOA, F ~func(context.Context) (R, error), GIOA ~func() E.Either[error, R], R any](f func() GRA) F {
	return func(c context.Context) (R, error) {
		return E.UnwrapError(f()(c)())
	}
}

// Eitherize1 converts a function with 1 parameters returning a tuple into a function with 1 parameters returning a [GRA]
// The inverse function is [Uneitherize1]
func Eitherize1[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0) (R, error), GIOA ~func() E.Either[error, R], T0, R any](f F) func(T0) GRA {
	return RE.Eitherize1[GRA](f)
}

// Uneitherize1 converts a function with 1 parameters returning a [GRA] into a function with 1 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize1[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0) (R, error), GIOA ~func() E.Either[error, R], T0, R any](f func(T0) GRA) F {
	return func(c context.Context, t0 T0) (R, error) {
		return E.UnwrapError(f(t0)(c)())
	}
}

// SequenceT1 converts 1 readers into a reader of a [T.Tuple1].
func SequenceT1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](
	t1 GR_T1,
) GR_TUPLE1 {
	return A.SequenceT1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t1,
	)
}

// SequenceSeqT1 converts 1 readers into a reader of a [T.Tuple1].
func SequenceSeqT1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](
	t1 GR_T1,
) GR_TUPLE1 {
	return A.SequenceT1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t1,
	)
}

// SequenceParT1 converts 1 readers into a reader of a [T.Tuple1].
func SequenceParT1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](
	t1 GR_T1,
) GR_TUPLE1 {
	return A.SequenceT1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t1,
	)
}

// SequenceTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func SequenceTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](t T.Tuple1[GR_T1]) GR_TUPLE1 {
	return A.SequenceTuple1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t,
	)
}

// SequenceSeqTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func SequenceSeqTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](t T.Tuple1[GR_T1]) GR_TUPLE1 {
	return A.SequenceTuple1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t,
	)
}

// SequenceParTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func SequenceParTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	T1 any](t T.Tuple1[GR_T1]) GR_TUPLE1 {
	return A.SequenceTuple1(
		Map[GR_T1, GR_TUPLE1, GIO_T1],
		t,
	)
}

// TraverseTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func TraverseTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	F1 ~func(A1) GR_T1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	A1,
	T1 any](f1 F1) func(T.Tuple1[A1]) GR_TUPLE1 {
	return func(t T.Tuple1[A1]) GR_TUPLE1 {
		return A.TraverseTuple1(
			Map[GR_T1, GR_TUPLE1, GIO_T1],
			f1,
			t,
		)
	}
}

// TraverseSeqTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func TraverseSeqTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	F1 ~func(A1) GR_T1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	A1,
	T1 any](f1 F1) func(T.Tuple1[A1]) GR_TUPLE1 {
	return func(t T.Tuple1[A1]) GR_TUPLE1 {
		return A.TraverseTuple1(
			Map[GR_T1, GR_TUPLE1, GIO_T1],
			f1,
			t,
		)
	}
}

// TraverseParTuple1 converts a [T.Tuple1] of readers into a reader of a [T.Tuple1].
func TraverseParTuple1[
	GR_TUPLE1 ~func(context.Context) GIO_TUPLE1,
	F1 ~func(A1) GR_T1,
	GR_T1 ~func(context.Context) GIO_T1,
	GIO_TUPLE1 ~func() E.Either[error, T.Tuple1[T1]],
	GIO_T1 ~func() E.Either[error, T1],
	A1,
	T1 any](f1 F1) func(T.Tuple1[A1]) GR_TUPLE1 {
	return func(t T.Tuple1[A1]) GR_TUPLE1 {
		return A.TraverseTuple1(
			Map[GR_T1, GR_TUPLE1, GIO_T1],
			f1,
			t,
		)
	}
}

// Eitherize2 converts a function with 2 parameters returning a tuple into a function with 2 parameters returning a [GRA]
// The inverse function is [Uneitherize2]
func Eitherize2[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1) (R, error), GIOA ~func() E.Either[error, R], T0, T1, R any](f F) func(T0, T1) GRA {
	return RE.Eitherize2[GRA](f)
}

// Uneitherize2 converts a function with 2 parameters returning a [GRA] into a function with 2 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize2[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1) (R, error), GIOA ~func() E.Either[error, R], T0, T1, R any](f func(T0, T1) GRA) F {
	return func(c context.Context, t0 T0, t1 T1) (R, error) {
		return E.UnwrapError(f(t0, t1)(c)())
	}
}

// SequenceT2 converts 2 readers into a reader of a [T.Tuple2].
func SequenceT2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](
	t1 GR_T1,
	t2 GR_T2,
) GR_TUPLE2 {
	return A.SequenceT2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		Ap[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t1,
		t2,
	)
}

// SequenceSeqT2 converts 2 readers into a reader of a [T.Tuple2].
func SequenceSeqT2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](
	t1 GR_T1,
	t2 GR_T2,
) GR_TUPLE2 {
	return A.SequenceT2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		ApSeq[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t1,
		t2,
	)
}

// SequenceParT2 converts 2 readers into a reader of a [T.Tuple2].
func SequenceParT2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](
	t1 GR_T1,
	t2 GR_T2,
) GR_TUPLE2 {
	return A.SequenceT2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		ApPar[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t1,
		t2,
	)
}

// SequenceTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func SequenceTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](t T.Tuple2[GR_T1, GR_T2]) GR_TUPLE2 {
	return A.SequenceTuple2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		Ap[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t,
	)
}

// SequenceSeqTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func SequenceSeqTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](t T.Tuple2[GR_T1, GR_T2]) GR_TUPLE2 {
	return A.SequenceTuple2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		ApSeq[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t,
	)
}

// SequenceParTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func SequenceParTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	T1,
	T2 any](t T.Tuple2[GR_T1, GR_T2]) GR_TUPLE2 {
	return A.SequenceTuple2(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
		ApPar[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
		t,
	)
}

// TraverseTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func TraverseTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	A1,
	T1,
	A2,
	T2 any](f1 F1, f2 F2) func(T.Tuple2[A1, A2]) GR_TUPLE2 {
	return func(t T.Tuple2[A1, A2]) GR_TUPLE2 {
		return A.TraverseTuple2(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
			Ap[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
			f1,
			f2,
			t,
		)
	}
}

// TraverseSeqTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func TraverseSeqTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	A1,
	T1,
	A2,
	T2 any](f1 F1, f2 F2) func(T.Tuple2[A1, A2]) GR_TUPLE2 {
	return func(t T.Tuple2[A1, A2]) GR_TUPLE2 {
		return A.TraverseTuple2(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
			ApSeq[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
			f1,
			f2,
			t,
		)
	}
}

// TraverseParTuple2 converts a [T.Tuple2] of readers into a reader of a [T.Tuple2].
func TraverseParTuple2[
	GR_TUPLE2 ~func(context.Context) GIO_TUPLE2,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GIO_TUPLE2 ~func() E.Either[error, T.Tuple2[T1, T2]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	A1,
	T1,
	A2,
	T2 any](f1 F1, f2 F2) func(T.Tuple2[A1, A2]) GR_TUPLE2 {
	return func(t T.Tuple2[A1, A2]) GR_TUPLE2 {
		return A.TraverseTuple2(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GIO_T1],
			ApPar[GR_TUPLE2, func(context.Context) func() E.Either[error, func(T2) T.Tuple2[T1, T2]], GR_T2],
			f1,
			f2,
			t,
		)
	}
}

// Eitherize3 converts a function with 3 parameters returning a tuple into a function with 3 parameters returning a [GRA]
// The inverse function is [Uneitherize3]
func Eitherize3[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, R any](f F) func(T0, T1, T2) GRA {
	return RE.Eitherize3[GRA](f)
}

// Uneitherize3 converts a function with 3 parameters returning a [GRA] into a function with 3 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize3[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, R any](f func(T0, T1, T2) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2) (R, error) {
		return E.UnwrapError(f(t0, t1, t2)(c)())
	}
}

// SequenceT3 converts 3 readers into a reader of a [T.Tuple3].
func SequenceT3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
) GR_TUPLE3 {
	return A.SequenceT3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		Ap[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t1,
		t2,
		t3,
	)
}

// SequenceSeqT3 converts 3 readers into a reader of a [T.Tuple3].
func SequenceSeqT3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
) GR_TUPLE3 {
	return A.SequenceT3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		ApSeq[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t1,
		t2,
		t3,
	)
}

// SequenceParT3 converts 3 readers into a reader of a [T.Tuple3].
func SequenceParT3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
) GR_TUPLE3 {
	return A.SequenceT3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		ApPar[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t1,
		t2,
		t3,
	)
}

// SequenceTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func SequenceTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](t T.Tuple3[GR_T1, GR_T2, GR_T3]) GR_TUPLE3 {
	return A.SequenceTuple3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		Ap[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t,
	)
}

// SequenceSeqTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func SequenceSeqTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](t T.Tuple3[GR_T1, GR_T2, GR_T3]) GR_TUPLE3 {
	return A.SequenceTuple3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		ApSeq[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t,
	)
}

// SequenceParTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func SequenceParTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	T1,
	T2,
	T3 any](t T.Tuple3[GR_T1, GR_T2, GR_T3]) GR_TUPLE3 {
	return A.SequenceTuple3(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
		ApPar[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
		t,
	)
}

// TraverseTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func TraverseTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3 any](f1 F1, f2 F2, f3 F3) func(T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
	return func(t T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
		return A.TraverseTuple3(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
			Ap[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
			f1,
			f2,
			f3,
			t,
		)
	}
}

// TraverseSeqTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func TraverseSeqTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3 any](f1 F1, f2 F2, f3 F3) func(T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
	return func(t T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
		return A.TraverseTuple3(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
			ApSeq[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
			f1,
			f2,
			f3,
			t,
		)
	}
}

// TraverseParTuple3 converts a [T.Tuple3] of readers into a reader of a [T.Tuple3].
func TraverseParTuple3[
	GR_TUPLE3 ~func(context.Context) GIO_TUPLE3,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GIO_TUPLE3 ~func() E.Either[error, T.Tuple3[T1, T2, T3]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3 any](f1 F1, f2 F2, f3 F3) func(T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
	return func(t T.Tuple3[A1, A2, A3]) GR_TUPLE3 {
		return A.TraverseTuple3(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], func(context.Context) func() E.Either[error, func(T2) func(T3) T.Tuple3[T1, T2, T3]], GR_T2],
			ApPar[GR_TUPLE3, func(context.Context) func() E.Either[error, func(T3) T.Tuple3[T1, T2, T3]], GR_T3],
			f1,
			f2,
			f3,
			t,
		)
	}
}

// Eitherize4 converts a function with 4 parameters returning a tuple into a function with 4 parameters returning a [GRA]
// The inverse function is [Uneitherize4]
func Eitherize4[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, R any](f F) func(T0, T1, T2, T3) GRA {
	return RE.Eitherize4[GRA](f)
}

// Uneitherize4 converts a function with 4 parameters returning a [GRA] into a function with 4 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize4[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, R any](f func(T0, T1, T2, T3) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3)(c)())
	}
}

// SequenceT4 converts 4 readers into a reader of a [T.Tuple4].
func SequenceT4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
) GR_TUPLE4 {
	return A.SequenceT4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		Ap[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t1,
		t2,
		t3,
		t4,
	)
}

// SequenceSeqT4 converts 4 readers into a reader of a [T.Tuple4].
func SequenceSeqT4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
) GR_TUPLE4 {
	return A.SequenceT4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		ApSeq[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t1,
		t2,
		t3,
		t4,
	)
}

// SequenceParT4 converts 4 readers into a reader of a [T.Tuple4].
func SequenceParT4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
) GR_TUPLE4 {
	return A.SequenceT4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		ApPar[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t1,
		t2,
		t3,
		t4,
	)
}

// SequenceTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func SequenceTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](t T.Tuple4[GR_T1, GR_T2, GR_T3, GR_T4]) GR_TUPLE4 {
	return A.SequenceTuple4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		Ap[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t,
	)
}

// SequenceSeqTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func SequenceSeqTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](t T.Tuple4[GR_T1, GR_T2, GR_T3, GR_T4]) GR_TUPLE4 {
	return A.SequenceTuple4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		ApSeq[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t,
	)
}

// SequenceParTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func SequenceParTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	T1,
	T2,
	T3,
	T4 any](t T.Tuple4[GR_T1, GR_T2, GR_T3, GR_T4]) GR_TUPLE4 {
	return A.SequenceTuple4(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
		ApPar[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
		t,
	)
}

// TraverseTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func TraverseTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4 any](f1 F1, f2 F2, f3 F3, f4 F4) func(T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
	return func(t T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
		return A.TraverseTuple4(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
			Ap[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
			f1,
			f2,
			f3,
			f4,
			t,
		)
	}
}

// TraverseSeqTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func TraverseSeqTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4 any](f1 F1, f2 F2, f3 F3, f4 F4) func(T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
	return func(t T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
		return A.TraverseTuple4(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
			ApSeq[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
			f1,
			f2,
			f3,
			f4,
			t,
		)
	}
}

// TraverseParTuple4 converts a [T.Tuple4] of readers into a reader of a [T.Tuple4].
func TraverseParTuple4[
	GR_TUPLE4 ~func(context.Context) GIO_TUPLE4,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GIO_TUPLE4 ~func() E.Either[error, T.Tuple4[T1, T2, T3, T4]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4 any](f1 F1, f2 F2, f3 F3, f4 F4) func(T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
	return func(t T.Tuple4[A1, A2, A3, A4]) GR_TUPLE4 {
		return A.TraverseTuple4(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], func(context.Context) func() E.Either[error, func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T3],
			ApPar[GR_TUPLE4, func(context.Context) func() E.Either[error, func(T4) T.Tuple4[T1, T2, T3, T4]], GR_T4],
			f1,
			f2,
			f3,
			f4,
			t,
		)
	}
}

// Eitherize5 converts a function with 5 parameters returning a tuple into a function with 5 parameters returning a [GRA]
// The inverse function is [Uneitherize5]
func Eitherize5[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, R any](f F) func(T0, T1, T2, T3, T4) GRA {
	return RE.Eitherize5[GRA](f)
}

// Uneitherize5 converts a function with 5 parameters returning a [GRA] into a function with 5 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize5[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, R any](f func(T0, T1, T2, T3, T4) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4)(c)())
	}
}

// SequenceT5 converts 5 readers into a reader of a [T.Tuple5].
func SequenceT5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
) GR_TUPLE5 {
	return A.SequenceT5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		Ap[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t1,
		t2,
		t3,
		t4,
		t5,
	)
}

// SequenceSeqT5 converts 5 readers into a reader of a [T.Tuple5].
func SequenceSeqT5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
) GR_TUPLE5 {
	return A.SequenceT5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		ApSeq[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t1,
		t2,
		t3,
		t4,
		t5,
	)
}

// SequenceParT5 converts 5 readers into a reader of a [T.Tuple5].
func SequenceParT5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
) GR_TUPLE5 {
	return A.SequenceT5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		ApPar[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t1,
		t2,
		t3,
		t4,
		t5,
	)
}

// SequenceTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func SequenceTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](t T.Tuple5[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5]) GR_TUPLE5 {
	return A.SequenceTuple5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		Ap[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t,
	)
}

// SequenceSeqTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func SequenceSeqTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](t T.Tuple5[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5]) GR_TUPLE5 {
	return A.SequenceTuple5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		ApSeq[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t,
	)
}

// SequenceParTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func SequenceParTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	T1,
	T2,
	T3,
	T4,
	T5 any](t T.Tuple5[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5]) GR_TUPLE5 {
	return A.SequenceTuple5(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
		ApPar[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
		t,
	)
}

// TraverseTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func TraverseTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5) func(T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
	return func(t T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
		return A.TraverseTuple5(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
			Ap[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
			f1,
			f2,
			f3,
			f4,
			f5,
			t,
		)
	}
}

// TraverseSeqTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func TraverseSeqTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5) func(T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
	return func(t T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
		return A.TraverseTuple5(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
			ApSeq[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
			f1,
			f2,
			f3,
			f4,
			f5,
			t,
		)
	}
}

// TraverseParTuple5 converts a [T.Tuple5] of readers into a reader of a [T.Tuple5].
func TraverseParTuple5[
	GR_TUPLE5 ~func(context.Context) GIO_TUPLE5,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GIO_TUPLE5 ~func() E.Either[error, T.Tuple5[T1, T2, T3, T4, T5]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5) func(T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
	return func(t T.Tuple5[A1, A2, A3, A4, A5]) GR_TUPLE5 {
		return A.TraverseTuple5(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], func(context.Context) func() E.Either[error, func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T4],
			ApPar[GR_TUPLE5, func(context.Context) func() E.Either[error, func(T5) T.Tuple5[T1, T2, T3, T4, T5]], GR_T5],
			f1,
			f2,
			f3,
			f4,
			f5,
			t,
		)
	}
}

// Eitherize6 converts a function with 6 parameters returning a tuple into a function with 6 parameters returning a [GRA]
// The inverse function is [Uneitherize6]
func Eitherize6[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, R any](f F) func(T0, T1, T2, T3, T4, T5) GRA {
	return RE.Eitherize6[GRA](f)
}

// Uneitherize6 converts a function with 6 parameters returning a [GRA] into a function with 6 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize6[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, R any](f func(T0, T1, T2, T3, T4, T5) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4, t5 T5) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4, t5)(c)())
	}
}

// SequenceT6 converts 6 readers into a reader of a [T.Tuple6].
func SequenceT6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
) GR_TUPLE6 {
	return A.SequenceT6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		Ap[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
	)
}

// SequenceSeqT6 converts 6 readers into a reader of a [T.Tuple6].
func SequenceSeqT6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
) GR_TUPLE6 {
	return A.SequenceT6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		ApSeq[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
	)
}

// SequenceParT6 converts 6 readers into a reader of a [T.Tuple6].
func SequenceParT6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
) GR_TUPLE6 {
	return A.SequenceT6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		ApPar[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
	)
}

// SequenceTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func SequenceTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](t T.Tuple6[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6]) GR_TUPLE6 {
	return A.SequenceTuple6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		Ap[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t,
	)
}

// SequenceSeqTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func SequenceSeqTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](t T.Tuple6[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6]) GR_TUPLE6 {
	return A.SequenceTuple6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		ApSeq[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t,
	)
}

// SequenceParTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func SequenceParTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6 any](t T.Tuple6[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6]) GR_TUPLE6 {
	return A.SequenceTuple6(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
		ApPar[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
		t,
	)
}

// TraverseTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func TraverseTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6) func(T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
	return func(t T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
		return A.TraverseTuple6(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
			Ap[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
			Ap[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			t,
		)
	}
}

// TraverseSeqTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func TraverseSeqTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6) func(T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
	return func(t T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
		return A.TraverseTuple6(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
			ApSeq[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
			ApSeq[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			t,
		)
	}
}

// TraverseParTuple6 converts a [T.Tuple6] of readers into a reader of a [T.Tuple6].
func TraverseParTuple6[
	GR_TUPLE6 ~func(context.Context) GIO_TUPLE6,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GIO_TUPLE6 ~func() E.Either[error, T.Tuple6[T1, T2, T3, T4, T5, T6]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6) func(T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
	return func(t T.Tuple6[A1, A2, A3, A4, A5, A6]) GR_TUPLE6 {
		return A.TraverseTuple6(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T4],
			ApPar[func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], func(context.Context) func() E.Either[error, func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T5],
			ApPar[GR_TUPLE6, func(context.Context) func() E.Either[error, func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]], GR_T6],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			t,
		)
	}
}

// Eitherize7 converts a function with 7 parameters returning a tuple into a function with 7 parameters returning a [GRA]
// The inverse function is [Uneitherize7]
func Eitherize7[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, R any](f F) func(T0, T1, T2, T3, T4, T5, T6) GRA {
	return RE.Eitherize7[GRA](f)
}

// Uneitherize7 converts a function with 7 parameters returning a [GRA] into a function with 7 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize7[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, R any](f func(T0, T1, T2, T3, T4, T5, T6) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4, t5 T5, t6 T6) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4, t5, t6)(c)())
	}
}

// SequenceT7 converts 7 readers into a reader of a [T.Tuple7].
func SequenceT7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
) GR_TUPLE7 {
	return A.SequenceT7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		Ap[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
	)
}

// SequenceSeqT7 converts 7 readers into a reader of a [T.Tuple7].
func SequenceSeqT7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
) GR_TUPLE7 {
	return A.SequenceT7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		ApSeq[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
	)
}

// SequenceParT7 converts 7 readers into a reader of a [T.Tuple7].
func SequenceParT7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
) GR_TUPLE7 {
	return A.SequenceT7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		ApPar[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
	)
}

// SequenceTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func SequenceTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](t T.Tuple7[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7]) GR_TUPLE7 {
	return A.SequenceTuple7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		Ap[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t,
	)
}

// SequenceSeqTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func SequenceSeqTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](t T.Tuple7[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7]) GR_TUPLE7 {
	return A.SequenceTuple7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		ApSeq[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t,
	)
}

// SequenceParTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func SequenceParTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7 any](t T.Tuple7[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7]) GR_TUPLE7 {
	return A.SequenceTuple7(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
		ApPar[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
		t,
	)
}

// TraverseTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func TraverseTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7) func(T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
	return func(t T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
		return A.TraverseTuple7(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
			Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
			Ap[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
			Ap[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			t,
		)
	}
}

// TraverseSeqTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func TraverseSeqTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7) func(T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
	return func(t T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
		return A.TraverseTuple7(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
			ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
			ApSeq[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
			ApSeq[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			t,
		)
	}
}

// TraverseParTuple7 converts a [T.Tuple7] of readers into a reader of a [T.Tuple7].
func TraverseParTuple7[
	GR_TUPLE7 ~func(context.Context) GIO_TUPLE7,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GIO_TUPLE7 ~func() E.Either[error, T.Tuple7[T1, T2, T3, T4, T5, T6, T7]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7) func(T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
	return func(t T.Tuple7[A1, A2, A3, A4, A5, A6, A7]) GR_TUPLE7 {
		return A.TraverseTuple7(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T4],
			ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T5],
			ApPar[func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], func(context.Context) func() E.Either[error, func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T6],
			ApPar[GR_TUPLE7, func(context.Context) func() E.Either[error, func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]], GR_T7],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			t,
		)
	}
}

// Eitherize8 converts a function with 8 parameters returning a tuple into a function with 8 parameters returning a [GRA]
// The inverse function is [Uneitherize8]
func Eitherize8[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, R any](f F) func(T0, T1, T2, T3, T4, T5, T6, T7) GRA {
	return RE.Eitherize8[GRA](f)
}

// Uneitherize8 converts a function with 8 parameters returning a [GRA] into a function with 8 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize8[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, R any](f func(T0, T1, T2, T3, T4, T5, T6, T7) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4, t5 T5, t6 T6, t7 T7) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4, t5, t6, t7)(c)())
	}
}

// SequenceT8 converts 8 readers into a reader of a [T.Tuple8].
func SequenceT8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
) GR_TUPLE8 {
	return A.SequenceT8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		Ap[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
	)
}

// SequenceSeqT8 converts 8 readers into a reader of a [T.Tuple8].
func SequenceSeqT8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
) GR_TUPLE8 {
	return A.SequenceT8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		ApSeq[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
	)
}

// SequenceParT8 converts 8 readers into a reader of a [T.Tuple8].
func SequenceParT8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
) GR_TUPLE8 {
	return A.SequenceT8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		ApPar[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
	)
}

// SequenceTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func SequenceTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](t T.Tuple8[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8]) GR_TUPLE8 {
	return A.SequenceTuple8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		Ap[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t,
	)
}

// SequenceSeqTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func SequenceSeqTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](t T.Tuple8[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8]) GR_TUPLE8 {
	return A.SequenceTuple8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		ApSeq[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t,
	)
}

// SequenceParTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func SequenceParTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8 any](t T.Tuple8[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8]) GR_TUPLE8 {
	return A.SequenceTuple8(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
		ApPar[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
		t,
	)
}

// TraverseTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func TraverseTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8) func(T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
	return func(t T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
		return A.TraverseTuple8(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
			Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
			Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
			Ap[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
			Ap[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			t,
		)
	}
}

// TraverseSeqTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func TraverseSeqTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8) func(T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
	return func(t T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
		return A.TraverseTuple8(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
			ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
			ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
			ApSeq[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
			ApSeq[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			t,
		)
	}
}

// TraverseParTuple8 converts a [T.Tuple8] of readers into a reader of a [T.Tuple8].
func TraverseParTuple8[
	GR_TUPLE8 ~func(context.Context) GIO_TUPLE8,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GIO_TUPLE8 ~func() E.Either[error, T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8) func(T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
	return func(t T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8]) GR_TUPLE8 {
		return A.TraverseTuple8(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T4],
			ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T5],
			ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T6],
			ApPar[func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], func(context.Context) func() E.Either[error, func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T7],
			ApPar[GR_TUPLE8, func(context.Context) func() E.Either[error, func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]], GR_T8],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			t,
		)
	}
}

// Eitherize9 converts a function with 9 parameters returning a tuple into a function with 9 parameters returning a [GRA]
// The inverse function is [Uneitherize9]
func Eitherize9[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7, T8) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, T8, R any](f F) func(T0, T1, T2, T3, T4, T5, T6, T7, T8) GRA {
	return RE.Eitherize9[GRA](f)
}

// Uneitherize9 converts a function with 9 parameters returning a [GRA] into a function with 9 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize9[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7, T8) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, T8, R any](f func(T0, T1, T2, T3, T4, T5, T6, T7, T8) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4, t5 T5, t6 T6, t7 T7, t8 T8) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4, t5, t6, t7, t8)(c)())
	}
}

// SequenceT9 converts 9 readers into a reader of a [T.Tuple9].
func SequenceT9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
) GR_TUPLE9 {
	return A.SequenceT9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		Ap[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		Ap[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
	)
}

// SequenceSeqT9 converts 9 readers into a reader of a [T.Tuple9].
func SequenceSeqT9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
) GR_TUPLE9 {
	return A.SequenceT9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		ApSeq[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		ApSeq[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
	)
}

// SequenceParT9 converts 9 readers into a reader of a [T.Tuple9].
func SequenceParT9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
) GR_TUPLE9 {
	return A.SequenceT9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		ApPar[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		ApPar[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
	)
}

// SequenceTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func SequenceTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](t T.Tuple9[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9]) GR_TUPLE9 {
	return A.SequenceTuple9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		Ap[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		Ap[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t,
	)
}

// SequenceSeqTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func SequenceSeqTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](t T.Tuple9[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9]) GR_TUPLE9 {
	return A.SequenceTuple9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		ApSeq[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		ApSeq[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t,
	)
}

// SequenceParTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func SequenceParTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9 any](t T.Tuple9[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9]) GR_TUPLE9 {
	return A.SequenceTuple9(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
		ApPar[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
		ApPar[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
		t,
	)
}

// TraverseTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func TraverseTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9) func(T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
	return func(t T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
		return A.TraverseTuple9(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
			Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
			Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
			Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
			Ap[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
			Ap[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			t,
		)
	}
}

// TraverseSeqTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func TraverseSeqTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9) func(T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
	return func(t T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
		return A.TraverseTuple9(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
			ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
			ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
			ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
			ApSeq[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
			ApSeq[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			t,
		)
	}
}

// TraverseParTuple9 converts a [T.Tuple9] of readers into a reader of a [T.Tuple9].
func TraverseParTuple9[
	GR_TUPLE9 ~func(context.Context) GIO_TUPLE9,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GIO_TUPLE9 ~func() E.Either[error, T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9) func(T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
	return func(t T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9]) GR_TUPLE9 {
		return A.TraverseTuple9(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T4],
			ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T5],
			ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T6],
			ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T7],
			ApPar[func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], func(context.Context) func() E.Either[error, func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T8],
			ApPar[GR_TUPLE9, func(context.Context) func() E.Either[error, func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]], GR_T9],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			t,
		)
	}
}

// Eitherize10 converts a function with 10 parameters returning a tuple into a function with 10 parameters returning a [GRA]
// The inverse function is [Uneitherize10]
func Eitherize10[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7, T8, T9) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, R any](f F) func(T0, T1, T2, T3, T4, T5, T6, T7, T8, T9) GRA {
	return RE.Eitherize10[GRA](f)
}

// Uneitherize10 converts a function with 10 parameters returning a [GRA] into a function with 10 parameters returning a tuple.
// The first parameter is considered to be the [context.Context].
func Uneitherize10[GRA ~func(context.Context) GIOA, F ~func(context.Context, T0, T1, T2, T3, T4, T5, T6, T7, T8, T9) (R, error), GIOA ~func() E.Either[error, R], T0, T1, T2, T3, T4, T5, T6, T7, T8, T9, R any](f func(T0, T1, T2, T3, T4, T5, T6, T7, T8, T9) GRA) F {
	return func(c context.Context, t0 T0, t1 T1, t2 T2, t3 T3, t4 T4, t5 T5, t6 T6, t7 T7, t8 T8, t9 T9) (R, error) {
		return E.UnwrapError(f(t0, t1, t2, t3, t4, t5, t6, t7, t8, t9)(c)())
	}
}

// SequenceT10 converts 10 readers into a reader of a [T.Tuple10].
func SequenceT10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
	t10 GR_T10,
) GR_TUPLE10 {
	return A.SequenceT10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		Ap[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		Ap[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		Ap[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
		t10,
	)
}

// SequenceSeqT10 converts 10 readers into a reader of a [T.Tuple10].
func SequenceSeqT10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
	t10 GR_T10,
) GR_TUPLE10 {
	return A.SequenceT10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		ApSeq[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		ApSeq[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		ApSeq[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
		t10,
	)
}

// SequenceParT10 converts 10 readers into a reader of a [T.Tuple10].
func SequenceParT10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](
	t1 GR_T1,
	t2 GR_T2,
	t3 GR_T3,
	t4 GR_T4,
	t5 GR_T5,
	t6 GR_T6,
	t7 GR_T7,
	t8 GR_T8,
	t9 GR_T9,
	t10 GR_T10,
) GR_TUPLE10 {
	return A.SequenceT10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		ApPar[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		ApPar[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		ApPar[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t1,
		t2,
		t3,
		t4,
		t5,
		t6,
		t7,
		t8,
		t9,
		t10,
	)
}

// SequenceTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func SequenceTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](t T.Tuple10[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9, GR_T10]) GR_TUPLE10 {
	return A.SequenceTuple10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		Ap[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		Ap[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		Ap[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t,
	)
}

// SequenceSeqTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func SequenceSeqTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](t T.Tuple10[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9, GR_T10]) GR_TUPLE10 {
	return A.SequenceTuple10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		ApSeq[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		ApSeq[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		ApSeq[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t,
	)
}

// SequenceParTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func SequenceParTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10 any](t T.Tuple10[GR_T1, GR_T2, GR_T3, GR_T4, GR_T5, GR_T6, GR_T7, GR_T8, GR_T9, GR_T10]) GR_TUPLE10 {
	return A.SequenceTuple10(
		Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
		ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
		ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
		ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
		ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
		ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
		ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
		ApPar[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
		ApPar[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
		ApPar[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
		t,
	)
}

// TraverseTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func TraverseTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	F10 ~func(A10) GR_T10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9,
	A10,
	T10 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9, f10 F10) func(T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
	return func(t T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
		return A.TraverseTuple10(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
			Ap[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
			Ap[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
			Ap[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
			Ap[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
			Ap[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
			Ap[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
			Ap[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
			Ap[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
			Ap[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			f10,
			t,
		)
	}
}

// TraverseSeqTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func TraverseSeqTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	F10 ~func(A10) GR_T10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9,
	A10,
	T10 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9, f10 F10) func(T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
	return func(t T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
		return A.TraverseTuple10(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
			ApSeq[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
			ApSeq[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
			ApSeq[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
			ApSeq[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
			ApSeq[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
			ApSeq[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
			ApSeq[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
			ApSeq[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
			ApSeq[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			f10,
			t,
		)
	}
}

// TraverseParTuple10 converts a [T.Tuple10] of readers into a reader of a [T.Tuple10].
func TraverseParTuple10[
	GR_TUPLE10 ~func(context.Context) GIO_TUPLE10,
	F1 ~func(A1) GR_T1,
	F2 ~func(A2) GR_T2,
	F3 ~func(A3) GR_T3,
	F4 ~func(A4) GR_T4,
	F5 ~func(A5) GR_T5,
	F6 ~func(A6) GR_T6,
	F7 ~func(A7) GR_T7,
	F8 ~func(A8) GR_T8,
	F9 ~func(A9) GR_T9,
	F10 ~func(A10) GR_T10,
	GR_T1 ~func(context.Context) GIO_T1,
	GR_T2 ~func(context.Context) GIO_T2,
	GR_T3 ~func(context.Context) GIO_T3,
	GR_T4 ~func(context.Context) GIO_T4,
	GR_T5 ~func(context.Context) GIO_T5,
	GR_T6 ~func(context.Context) GIO_T6,
	GR_T7 ~func(context.Context) GIO_T7,
	GR_T8 ~func(context.Context) GIO_T8,
	GR_T9 ~func(context.Context) GIO_T9,
	GR_T10 ~func(context.Context) GIO_T10,
	GIO_TUPLE10 ~func() E.Either[error, T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]],
	GIO_T1 ~func() E.Either[error, T1],
	GIO_T2 ~func() E.Either[error, T2],
	GIO_T3 ~func() E.Either[error, T3],
	GIO_T4 ~func() E.Either[error, T4],
	GIO_T5 ~func() E.Either[error, T5],
	GIO_T6 ~func() E.Either[error, T6],
	GIO_T7 ~func() E.Either[error, T7],
	GIO_T8 ~func() E.Either[error, T8],
	GIO_T9 ~func() E.Either[error, T9],
	GIO_T10 ~func() E.Either[error, T10],
	A1,
	T1,
	A2,
	T2,
	A3,
	T3,
	A4,
	T4,
	A5,
	T5,
	A6,
	T6,
	A7,
	T7,
	A8,
	T8,
	A9,
	T9,
	A10,
	T10 any](f1 F1, f2 F2, f3 F3, f4 F4, f5 F5, f6 F6, f7 F7, f8 F8, f9 F9, f10 F10) func(T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
	return func(t T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10]) GR_TUPLE10 {
		return A.TraverseTuple10(
			Map[GR_T1, func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GIO_T1],
			ApPar[func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T2],
			ApPar[func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T3],
			ApPar[func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T4],
			ApPar[func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T5],
			ApPar[func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T6],
			ApPar[func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T7],
			ApPar[func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T8],
			ApPar[func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], func(context.Context) func() E.Either[error, func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T9],
			ApPar[GR_TUPLE10, func(context.Context) func() E.Either[error, func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]], GR_T10],
			f1,
			f2,
			f3,
			f4,
			f5,
			f6,
			f7,
			f8,
			f9,
			f10,
			t,
		)
	}
}
