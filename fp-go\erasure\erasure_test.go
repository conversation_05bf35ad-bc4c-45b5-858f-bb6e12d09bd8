// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package erasure

import (
	"fmt"
	"strings"
	"testing"

	E "github.com/IBM/fp-go/either"
	F "github.com/IBM/fp-go/function"
)

func TestEither(t *testing.T) {

	e1 := F.Pipe3(
		E.Of[error](Erase("Carsten")),
		<PERSON><PERSON>[error](Erase1(strings.ToUpper)),
		<PERSON><PERSON>GetOrElse(func(e error) any {
			return Erase("Error")
		}),
		Unerase[string],
	)

	fmt.Println(e1)
}
