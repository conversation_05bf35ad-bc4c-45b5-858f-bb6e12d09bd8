// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package array

// Prepend prepends a single value to an array
func Prepend[ENDO ~func(AS) AS, AS ~[]A, A any](head A) ENDO {
	return func(as AS) AS {
		l := len(as)
		cpy := make(AS, l+1)
		copy(cpy[1:], as)
		cpy[0] = head
		return cpy

	}
}
