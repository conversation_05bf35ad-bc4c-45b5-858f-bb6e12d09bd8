// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package file

import (
	"os"

	IOE "github.com/IBM/fp-go/ioeither"
)

// MkdirAll create a sequence of directories, see [os.MkdirAll]
func MkdirAll(path string, perm os.FileMode) IOE.IOEither[error, string] {
	return IOE.TryCatchError(func() (string, error) {
		return path, os.MkdirAll(path, perm)
	})
}

// Mkdir create a directory, see [os.Mkdir]
func Mkdir(path string, perm os.FileMode) IOE.IOEither[error, string] {
	return IOE.TryCatchError(func() (string, error) {
		return path, os.Mkdir(path, perm)
	})
}
