// Code generated by go generate; DO NOT EDIT.
// This file was generated by robots at
// 2024-02-29 16:19:01.120389 +0100 CET m=+0.011216001

package function

// Combinations for a total of 1 arguments

// Bind1of1 takes a function with 1 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [1] of the original function.
// The return value of is a function with the remaining 0 parameters at positions [] of the original function.
func Bind1of1[F ~func(T1) R, T1, R any](f F) func(T1) func() R {
	return func(t1 T1) func() R {
		return func() R {
			return f(t1)
		}
	}
}

// Ignore1of1 takes a function with 0 parameters and returns a new function with 1 parameters that will ignore the values at positions [1] and pass the remaining 0 parameters to the original function
func Ignore1of1[T1 any, F ~func() R, R any](f F) func(T1) R {
	return func(t1 T1) R {
		return f()
	}
}

// Combinations for a total of 2 arguments

// Bind1of2 takes a function with 2 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [1] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [2] of the original function.
func Bind1of2[F ~func(T1, T2) R, T1, T2, R any](f F) func(T1) func(T2) R {
	return func(t1 T1) func(T2) R {
		return func(t2 T2) R {
			return f(t1, t2)
		}
	}
}

// Ignore1of2 takes a function with 1 parameters and returns a new function with 2 parameters that will ignore the values at positions [1] and pass the remaining 1 parameters to the original function
func Ignore1of2[T1 any, F ~func(T2) R, T2, R any](f F) func(T1, T2) R {
	return func(t1 T1, t2 T2) R {
		return f(t2)
	}
}

// Bind2of2 takes a function with 2 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [2] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [1] of the original function.
func Bind2of2[F ~func(T1, T2) R, T1, T2, R any](f F) func(T2) func(T1) R {
	return func(t2 T2) func(T1) R {
		return func(t1 T1) R {
			return f(t1, t2)
		}
	}
}

// Ignore2of2 takes a function with 1 parameters and returns a new function with 2 parameters that will ignore the values at positions [2] and pass the remaining 1 parameters to the original function
func Ignore2of2[T2 any, F ~func(T1) R, T1, R any](f F) func(T1, T2) R {
	return func(t1 T1, t2 T2) R {
		return f(t1)
	}
}

// Bind12of2 takes a function with 2 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 2] of the original function.
// The return value of is a function with the remaining 0 parameters at positions [] of the original function.
func Bind12of2[F ~func(T1, T2) R, T1, T2, R any](f F) func(T1, T2) func() R {
	return func(t1 T1, t2 T2) func() R {
		return func() R {
			return f(t1, t2)
		}
	}
}

// Ignore12of2 takes a function with 0 parameters and returns a new function with 2 parameters that will ignore the values at positions [1, 2] and pass the remaining 0 parameters to the original function
func Ignore12of2[T1, T2 any, F ~func() R, R any](f F) func(T1, T2) R {
	return func(t1 T1, t2 T2) R {
		return f()
	}
}

// Combinations for a total of 3 arguments

// Bind1of3 takes a function with 3 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [1] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [2, 3] of the original function.
func Bind1of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T1) func(T2, T3) R {
	return func(t1 T1) func(T2, T3) R {
		return func(t2 T2, t3 T3) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore1of3 takes a function with 2 parameters and returns a new function with 3 parameters that will ignore the values at positions [1] and pass the remaining 2 parameters to the original function
func Ignore1of3[T1 any, F ~func(T2, T3) R, T2, T3, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t2, t3)
	}
}

// Bind2of3 takes a function with 3 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [2] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [1, 3] of the original function.
func Bind2of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T2) func(T1, T3) R {
	return func(t2 T2) func(T1, T3) R {
		return func(t1 T1, t3 T3) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore2of3 takes a function with 2 parameters and returns a new function with 3 parameters that will ignore the values at positions [2] and pass the remaining 2 parameters to the original function
func Ignore2of3[T2 any, F ~func(T1, T3) R, T1, T3, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t1, t3)
	}
}

// Bind3of3 takes a function with 3 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [3] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [1, 2] of the original function.
func Bind3of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T3) func(T1, T2) R {
	return func(t3 T3) func(T1, T2) R {
		return func(t1 T1, t2 T2) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore3of3 takes a function with 2 parameters and returns a new function with 3 parameters that will ignore the values at positions [3] and pass the remaining 2 parameters to the original function
func Ignore3of3[T3 any, F ~func(T1, T2) R, T1, T2, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t1, t2)
	}
}

// Bind12of3 takes a function with 3 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 2] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [3] of the original function.
func Bind12of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T1, T2) func(T3) R {
	return func(t1 T1, t2 T2) func(T3) R {
		return func(t3 T3) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore12of3 takes a function with 1 parameters and returns a new function with 3 parameters that will ignore the values at positions [1, 2] and pass the remaining 1 parameters to the original function
func Ignore12of3[T1, T2 any, F ~func(T3) R, T3, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t3)
	}
}

// Bind13of3 takes a function with 3 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 3] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [2] of the original function.
func Bind13of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T1, T3) func(T2) R {
	return func(t1 T1, t3 T3) func(T2) R {
		return func(t2 T2) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore13of3 takes a function with 1 parameters and returns a new function with 3 parameters that will ignore the values at positions [1, 3] and pass the remaining 1 parameters to the original function
func Ignore13of3[T1, T3 any, F ~func(T2) R, T2, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t2)
	}
}

// Bind23of3 takes a function with 3 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [2, 3] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [1] of the original function.
func Bind23of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T2, T3) func(T1) R {
	return func(t2 T2, t3 T3) func(T1) R {
		return func(t1 T1) R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore23of3 takes a function with 1 parameters and returns a new function with 3 parameters that will ignore the values at positions [2, 3] and pass the remaining 1 parameters to the original function
func Ignore23of3[T2, T3 any, F ~func(T1) R, T1, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f(t1)
	}
}

// Bind123of3 takes a function with 3 parameters and returns a new function with 3 parameters that will bind these parameters to the positions [1, 2, 3] of the original function.
// The return value of is a function with the remaining 0 parameters at positions [] of the original function.
func Bind123of3[F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T1, T2, T3) func() R {
	return func(t1 T1, t2 T2, t3 T3) func() R {
		return func() R {
			return f(t1, t2, t3)
		}
	}
}

// Ignore123of3 takes a function with 0 parameters and returns a new function with 3 parameters that will ignore the values at positions [1, 2, 3] and pass the remaining 0 parameters to the original function
func Ignore123of3[T1, T2, T3 any, F ~func() R, R any](f F) func(T1, T2, T3) R {
	return func(t1 T1, t2 T2, t3 T3) R {
		return f()
	}
}

// Combinations for a total of 4 arguments

// Bind1of4 takes a function with 4 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [1] of the original function.
// The return value of is a function with the remaining 3 parameters at positions [2, 3, 4] of the original function.
func Bind1of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1) func(T2, T3, T4) R {
	return func(t1 T1) func(T2, T3, T4) R {
		return func(t2 T2, t3 T3, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore1of4 takes a function with 3 parameters and returns a new function with 4 parameters that will ignore the values at positions [1] and pass the remaining 3 parameters to the original function
func Ignore1of4[T1 any, F ~func(T2, T3, T4) R, T2, T3, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t2, t3, t4)
	}
}

// Bind2of4 takes a function with 4 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [2] of the original function.
// The return value of is a function with the remaining 3 parameters at positions [1, 3, 4] of the original function.
func Bind2of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T2) func(T1, T3, T4) R {
	return func(t2 T2) func(T1, T3, T4) R {
		return func(t1 T1, t3 T3, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore2of4 takes a function with 3 parameters and returns a new function with 4 parameters that will ignore the values at positions [2] and pass the remaining 3 parameters to the original function
func Ignore2of4[T2 any, F ~func(T1, T3, T4) R, T1, T3, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t3, t4)
	}
}

// Bind3of4 takes a function with 4 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [3] of the original function.
// The return value of is a function with the remaining 3 parameters at positions [1, 2, 4] of the original function.
func Bind3of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T3) func(T1, T2, T4) R {
	return func(t3 T3) func(T1, T2, T4) R {
		return func(t1 T1, t2 T2, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore3of4 takes a function with 3 parameters and returns a new function with 4 parameters that will ignore the values at positions [3] and pass the remaining 3 parameters to the original function
func Ignore3of4[T3 any, F ~func(T1, T2, T4) R, T1, T2, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t2, t4)
	}
}

// Bind4of4 takes a function with 4 parameters and returns a new function with 1 parameters that will bind these parameters to the positions [4] of the original function.
// The return value of is a function with the remaining 3 parameters at positions [1, 2, 3] of the original function.
func Bind4of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T4) func(T1, T2, T3) R {
	return func(t4 T4) func(T1, T2, T3) R {
		return func(t1 T1, t2 T2, t3 T3) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore4of4 takes a function with 3 parameters and returns a new function with 4 parameters that will ignore the values at positions [4] and pass the remaining 3 parameters to the original function
func Ignore4of4[T4 any, F ~func(T1, T2, T3) R, T1, T2, T3, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t2, t3)
	}
}

// Bind12of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 2] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [3, 4] of the original function.
func Bind12of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T2) func(T3, T4) R {
	return func(t1 T1, t2 T2) func(T3, T4) R {
		return func(t3 T3, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore12of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 2] and pass the remaining 2 parameters to the original function
func Ignore12of4[T1, T2 any, F ~func(T3, T4) R, T3, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t3, t4)
	}
}

// Bind13of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 3] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [2, 4] of the original function.
func Bind13of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T3) func(T2, T4) R {
	return func(t1 T1, t3 T3) func(T2, T4) R {
		return func(t2 T2, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore13of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 3] and pass the remaining 2 parameters to the original function
func Ignore13of4[T1, T3 any, F ~func(T2, T4) R, T2, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t2, t4)
	}
}

// Bind14of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [1, 4] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [2, 3] of the original function.
func Bind14of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T4) func(T2, T3) R {
	return func(t1 T1, t4 T4) func(T2, T3) R {
		return func(t2 T2, t3 T3) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore14of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 4] and pass the remaining 2 parameters to the original function
func Ignore14of4[T1, T4 any, F ~func(T2, T3) R, T2, T3, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t2, t3)
	}
}

// Bind23of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [2, 3] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [1, 4] of the original function.
func Bind23of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T2, T3) func(T1, T4) R {
	return func(t2 T2, t3 T3) func(T1, T4) R {
		return func(t1 T1, t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore23of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [2, 3] and pass the remaining 2 parameters to the original function
func Ignore23of4[T2, T3 any, F ~func(T1, T4) R, T1, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t4)
	}
}

// Bind24of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [2, 4] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [1, 3] of the original function.
func Bind24of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T2, T4) func(T1, T3) R {
	return func(t2 T2, t4 T4) func(T1, T3) R {
		return func(t1 T1, t3 T3) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore24of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [2, 4] and pass the remaining 2 parameters to the original function
func Ignore24of4[T2, T4 any, F ~func(T1, T3) R, T1, T3, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t3)
	}
}

// Bind34of4 takes a function with 4 parameters and returns a new function with 2 parameters that will bind these parameters to the positions [3, 4] of the original function.
// The return value of is a function with the remaining 2 parameters at positions [1, 2] of the original function.
func Bind34of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T3, T4) func(T1, T2) R {
	return func(t3 T3, t4 T4) func(T1, T2) R {
		return func(t1 T1, t2 T2) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore34of4 takes a function with 2 parameters and returns a new function with 4 parameters that will ignore the values at positions [3, 4] and pass the remaining 2 parameters to the original function
func Ignore34of4[T3, T4 any, F ~func(T1, T2) R, T1, T2, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1, t2)
	}
}

// Bind123of4 takes a function with 4 parameters and returns a new function with 3 parameters that will bind these parameters to the positions [1, 2, 3] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [4] of the original function.
func Bind123of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T2, T3) func(T4) R {
	return func(t1 T1, t2 T2, t3 T3) func(T4) R {
		return func(t4 T4) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore123of4 takes a function with 1 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 2, 3] and pass the remaining 1 parameters to the original function
func Ignore123of4[T1, T2, T3 any, F ~func(T4) R, T4, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t4)
	}
}

// Bind124of4 takes a function with 4 parameters and returns a new function with 3 parameters that will bind these parameters to the positions [1, 2, 4] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [3] of the original function.
func Bind124of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T2, T4) func(T3) R {
	return func(t1 T1, t2 T2, t4 T4) func(T3) R {
		return func(t3 T3) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore124of4 takes a function with 1 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 2, 4] and pass the remaining 1 parameters to the original function
func Ignore124of4[T1, T2, T4 any, F ~func(T3) R, T3, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t3)
	}
}

// Bind134of4 takes a function with 4 parameters and returns a new function with 3 parameters that will bind these parameters to the positions [1, 3, 4] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [2] of the original function.
func Bind134of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T3, T4) func(T2) R {
	return func(t1 T1, t3 T3, t4 T4) func(T2) R {
		return func(t2 T2) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore134of4 takes a function with 1 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 3, 4] and pass the remaining 1 parameters to the original function
func Ignore134of4[T1, T3, T4 any, F ~func(T2) R, T2, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t2)
	}
}

// Bind234of4 takes a function with 4 parameters and returns a new function with 3 parameters that will bind these parameters to the positions [2, 3, 4] of the original function.
// The return value of is a function with the remaining 1 parameters at positions [1] of the original function.
func Bind234of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T2, T3, T4) func(T1) R {
	return func(t2 T2, t3 T3, t4 T4) func(T1) R {
		return func(t1 T1) R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore234of4 takes a function with 1 parameters and returns a new function with 4 parameters that will ignore the values at positions [2, 3, 4] and pass the remaining 1 parameters to the original function
func Ignore234of4[T2, T3, T4 any, F ~func(T1) R, T1, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f(t1)
	}
}

// Bind1234of4 takes a function with 4 parameters and returns a new function with 4 parameters that will bind these parameters to the positions [1, 2, 3, 4] of the original function.
// The return value of is a function with the remaining 0 parameters at positions [] of the original function.
func Bind1234of4[F ~func(T1, T2, T3, T4) R, T1, T2, T3, T4, R any](f F) func(T1, T2, T3, T4) func() R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) func() R {
		return func() R {
			return f(t1, t2, t3, t4)
		}
	}
}

// Ignore1234of4 takes a function with 0 parameters and returns a new function with 4 parameters that will ignore the values at positions [1, 2, 3, 4] and pass the remaining 0 parameters to the original function
func Ignore1234of4[T1, T2, T3, T4 any, F ~func() R, R any](f F) func(T1, T2, T3, T4) R {
	return func(t1 T1, t2 T2, t3 T3, t4 T4) R {
		return f()
	}
}
