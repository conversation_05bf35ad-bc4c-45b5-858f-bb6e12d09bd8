// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package cli

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	A "github.com/IBM/fp-go/array"
	C "github.com/urfave/cli/v2"
)

func nonGenericIO(param string) string {
	return fmt.Sprintf("IO[%s]", param)
}

func genericIO(param string) string {
	return fmt.Sprintf("func() %s", param)
}

var extrasIO = A.Empty[string]()

func generateIOSequenceT(f, fg *os.File, i int) {
	generateGenericSequenceT(nonGenericIO, genericIO, extrasIO)(f, fg, i)
}

func generateIOSequenceTuple(f, fg *os.File, i int) {
	generateGenericSequenceTuple(nonGenericIO, genericIO, extrasIO)(f, fg, i)
}

func generateIOTraverseTuple(f, fg *os.File, i int) {
	generateGenericTraverseTuple(nonGenericIO, genericIO, extrasIO)(f, fg, i)
}

func generateIOHelpers(filename string, count int) error {
	dir, err := os.Getwd()
	if err != nil {
		return err
	}
	absDir, err := filepath.Abs(dir)
	if err != nil {
		return err
	}
	pkg := filepath.Base(absDir)
	f, err := os.Create(filepath.Clean(filename))
	if err != nil {
		return err
	}
	defer f.Close()
	// construct subdirectory
	genFilename := filepath.Join("generic", filename)
	err = os.MkdirAll("generic", os.ModePerm)
	if err != nil {
		return err
	}
	fg, err := os.Create(filepath.Clean(genFilename))
	if err != nil {
		return err
	}
	defer fg.Close()

	// log
	log.Printf("Generating code in [%s] for package [%s] with [%d] repetitions ...", filename, pkg, count)

	// some header
	fmt.Fprintln(f, "// Code generated by go generate; DO NOT EDIT.")
	fmt.Fprintln(f, "// This file was generated by robots at")
	fmt.Fprintf(f, "// %s\n\n", time.Now())

	fmt.Fprintf(f, "package %s\n\n", pkg)

	fmt.Fprintf(f, `
import (
	G "github.com/IBM/fp-go/%s/generic"	
	T "github.com/IBM/fp-go/tuple"
)
`, pkg)

	// some header
	fmt.Fprintln(fg, "// Code generated by go generate; DO NOT EDIT.")
	fmt.Fprintln(fg, "// This file was generated by robots at")
	fmt.Fprintf(fg, "// %s\n", time.Now())

	fmt.Fprintf(fg, "package generic\n\n")

	fmt.Fprintf(fg, `
import (
	T "github.com/IBM/fp-go/tuple"
	A "github.com/IBM/fp-go/internal/apply"
)
`)

	for i := 1; i <= count; i++ {
		// sequenceT
		generateIOSequenceT(f, fg, i)
		// sequenceTuple
		generateIOSequenceTuple(f, fg, i)
		// traverseTuple
		generateIOTraverseTuple(f, fg, i)
	}

	return nil
}

func IOCommand() *C.Command {
	return &C.Command{
		Name:  "io",
		Usage: "generate code for IO",
		Flags: []C.Flag{
			flagCount,
			flagFilename,
		},
		Action: func(ctx *C.Context) error {
			return generateIOHelpers(
				ctx.String(keyFilename),
				ctx.Int(keyCount),
			)
		},
	}
}
