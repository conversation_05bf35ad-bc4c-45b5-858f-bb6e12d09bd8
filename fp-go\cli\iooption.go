// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package cli

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	A "github.com/IBM/fp-go/array"
	C "github.com/urfave/cli/v2"
)

func nonGenericIOOption(param string) string {
	return fmt.Sprintf("IOOption[%s]", param)
}

func genericIOOption(param string) string {
	return fmt.Sprintf("func() O.Option[%s]", param)
}

var extrasIOOption = A.Empty[string]()

func generateIOOptionSequenceT(f, fg *os.File, i int) {
	generateGenericSequenceT(nonGenericIOOption, genericIOOption, extrasIOOption)(f, fg, i)
}

func generateIOOptionSequenceTuple(f, fg *os.File, i int) {
	generateGenericSequenceTuple(nonGenericIOOption, genericIOOption, extrasIOOption)(f, fg, i)
}

func generateIOOptionTraverseTuple(f, fg *os.File, i int) {
	generateGenericTraverseTuple(nonGenericIOOption, genericIOOption, extrasIOOption)(f, fg, i)
}

func generateIOOptionHelpers(filename string, count int) error {
	dir, err := os.Getwd()
	if err != nil {
		return err
	}
	absDir, err := filepath.Abs(dir)
	if err != nil {
		return err
	}
	pkg := filepath.Base(absDir)
	f, err := os.Create(filepath.Clean(filename))
	if err != nil {
		return err
	}
	defer f.Close()
	// construct subdirectory
	genFilename := filepath.Join("generic", filename)
	err = os.MkdirAll("generic", os.ModePerm)
	if err != nil {
		return err
	}
	fg, err := os.Create(filepath.Clean(genFilename))
	if err != nil {
		return err
	}
	defer fg.Close()

	// log
	log.Printf("Generating code in [%s] for package [%s] with [%d] repetitions ...", filename, pkg, count)

	// some header
	fmt.Fprintln(f, "// Code generated by go generate; DO NOT EDIT.")
	fmt.Fprintln(f, "// This file was generated by robots at")
	fmt.Fprintf(f, "// %s\n\n", time.Now())

	fmt.Fprintf(f, "package %s\n\n", pkg)

	fmt.Fprintf(f, `
import (
	G "github.com/IBM/fp-go/%s/generic"	
	T "github.com/IBM/fp-go/tuple"
)
`, pkg)

	// some header
	fmt.Fprintln(fg, "// Code generated by go generate; DO NOT EDIT.")
	fmt.Fprintln(fg, "// This file was generated by robots at")
	fmt.Fprintf(fg, "// %s\n", time.Now())

	fmt.Fprintf(fg, "package generic\n\n")

	fmt.Fprintf(fg, `
import (
	T "github.com/IBM/fp-go/tuple"
	O "github.com/IBM/fp-go/option"
	A "github.com/IBM/fp-go/internal/apply"
)
`)

	for i := 1; i <= count; i++ {
		// sequenceT
		generateIOOptionSequenceT(f, fg, i)
		// sequenceTuple
		generateIOOptionSequenceTuple(f, fg, i)
		// traverseTuple
		generateIOOptionTraverseTuple(f, fg, i)
	}

	return nil
}

func IOOptionCommand() *C.Command {
	return &C.Command{
		Name:  "iooption",
		Usage: "generate code for IOOption",
		Flags: []C.Flag{
			flagCount,
			flagFilename,
		},
		Action: func(ctx *C.Context) error {
			return generateIOOptionHelpers(
				ctx.String(keyFilename),
				ctx.Int(keyCount),
			)
		},
	}
}
