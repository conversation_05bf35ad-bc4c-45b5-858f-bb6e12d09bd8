// Copyright (c) 2024 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package option

import (
	"github.com/IBM/fp-go/internal/functor"
)

type optionFunctor[A, B any] struct{}

func (o *optionFunctor[A, B]) Map(f func(A) B) func(Option[A]) Option[B] {
	return Map[A, B](f)
}

// Functor implements the functoric operations for [Option]
func Functor[A, B any]() functor.Functor[A, B, Option[A], Option[B]] {
	return &optionFunctor[A, B]{}
}
