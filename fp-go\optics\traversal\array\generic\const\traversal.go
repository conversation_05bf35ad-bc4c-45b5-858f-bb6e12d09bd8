// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package generic

import (
	C "github.com/IBM/fp-go/constant"
	M "github.com/IBM/fp-go/monoid"
	AR "github.com/IBM/fp-go/optics/traversal/array/generic"
	G "github.com/IBM/fp-go/optics/traversal/generic"
)

// FromArray returns a traversal from an array for the const monad
func FromArray[GA ~[]A, E, A any](m M.Monoid[E]) G.Traversal[GA, A, C.Const[E, GA], <PERSON><PERSON>st[E, A]] {
	return AR.FromArray[GA, GA, A, A, C.Const[E, A], <PERSON><PERSON>st[E, func(A) GA], C.Const[E, GA]](
		C.Of[E, GA](m),
		C.Map[E, GA, func(A) GA],
		C.Ap[E, A, GA](m),
	)
}
