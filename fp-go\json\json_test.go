// Copyright (c) 2023 IBM Corp.
// All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package json

import (
	"fmt"
	"testing"

	E "github.com/IBM/fp-go/either"
	F "github.com/IBM/fp-go/function"
	"github.com/stretchr/testify/assert"
)

type Json map[string]any

func TestJsonMarshal(t *testing.T) {

	resRight := Unmarshal[Json]([]byte("{\"a\": \"b\"}"))
	assert.True(t, <PERSON>.IsRight(resRight))

	resLeft := Unmarshal[Json]([]byte("{\"a\""))
	assert.True(t, E.IsLeft(resLeft))

	res1 := F.Pipe1(
		resRight,
		E.Chain(Marshal[Json]),
	)
	fmt.Println(res1)
}
