// Code generated by go generate; DO NOT EDIT.
// This file was generated by robots at
// 2024-02-29 16:19:04.4975371 +0100 CET m=+0.017695301

package apply

import (
	F "github.com/IBM/fp-go/function"
	T "github.com/IBM/fp-go/tuple"
)

// tupleConstructor1 returns a curried version of [T.MakeTuple1]
func tupleConstructor1[T1 any]() func(T1) T.Tuple1[T1] {
	return F.Curry1(T.MakeTuple1[T1])
}

// SequenceT1 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 1 higher higher kinded types and returns a higher kinded type of a [Tuple1] with the resolved values.
func SequenceT1[
	MAP ~func(func(T1) T.Tuple1[T1]) func(HKT_T1) HKT_TUPLE1,
	T1,
	HKT_T1, // HKT[T1]
	HKT_TUPLE1 any, // HKT[Tuple1[T1]]
](
	fmap MAP,
	t1 HKT_T1,
) HKT_TUPLE1 {
	return F.Pipe1(
		t1,
		fmap(tupleConstructor1[T1]()),
	)
}

// SequenceTuple1 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple1] of higher higher kinded types and returns a higher kinded type of a [Tuple1] with the resolved values.
func SequenceTuple1[
	MAP ~func(func(T1) T.Tuple1[T1]) func(HKT_T1) HKT_TUPLE1,
	T1,
	HKT_T1, // HKT[T1]
	HKT_TUPLE1 any, // HKT[Tuple1[T1]]
](
	fmap MAP,
	t T.Tuple1[HKT_T1],
) HKT_TUPLE1 {
	return F.Pipe1(
		t.F1,
		fmap(tupleConstructor1[T1]()),
	)
}

// TraverseTuple1 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple1] of base types and 1 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple1] with the resolved values.
func TraverseTuple1[
	MAP ~func(func(T1) T.Tuple1[T1]) func(HKT_T1) HKT_TUPLE1,
	F1 ~func(A1) HKT_T1,
	A1, T1,
	HKT_T1, // HKT[T1]
	HKT_TUPLE1 any, // HKT[Tuple1[T1]]
](
	fmap MAP,
	f1 F1,
	t T.Tuple1[A1],
) HKT_TUPLE1 {
	return F.Pipe1(
		f1(t.F1),
		fmap(tupleConstructor1[T1]()),
	)
}

// tupleConstructor2 returns a curried version of [T.MakeTuple2]
func tupleConstructor2[T1, T2 any]() func(T1) func(T2) T.Tuple2[T1, T2] {
	return F.Curry2(T.MakeTuple2[T1, T2])
}

// SequenceT2 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 2 higher higher kinded types and returns a higher kinded type of a [Tuple2] with the resolved values.
func SequenceT2[
	MAP ~func(func(T1) func(T2) T.Tuple2[T1, T2]) func(HKT_T1) HKT_F_T2,
	AP1 ~func(HKT_T2) func(HKT_F_T2) HKT_TUPLE2,
	T1,
	T2,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_F_T2, // HKT[func(T2) T.Tuple2[T1, T2]]
	HKT_TUPLE2 any, // HKT[Tuple2[T1, T2]]
](
	fmap MAP,
	fap1 AP1,
	t1 HKT_T1,
	t2 HKT_T2,
) HKT_TUPLE2 {
	return F.Pipe2(
		t1,
		fmap(tupleConstructor2[T1, T2]()),
		fap1(t2),
	)
}

// SequenceTuple2 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple2] of higher higher kinded types and returns a higher kinded type of a [Tuple2] with the resolved values.
func SequenceTuple2[
	MAP ~func(func(T1) func(T2) T.Tuple2[T1, T2]) func(HKT_T1) HKT_F_T2,
	AP1 ~func(HKT_T2) func(HKT_F_T2) HKT_TUPLE2,
	T1,
	T2,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_F_T2, // HKT[func(T2) T.Tuple2[T1, T2]]
	HKT_TUPLE2 any, // HKT[Tuple2[T1, T2]]
](
	fmap MAP,
	fap1 AP1,
	t T.Tuple2[HKT_T1, HKT_T2],
) HKT_TUPLE2 {
	return F.Pipe2(
		t.F1,
		fmap(tupleConstructor2[T1, T2]()),
		fap1(t.F2),
	)
}

// TraverseTuple2 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple2] of base types and 2 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple2] with the resolved values.
func TraverseTuple2[
	MAP ~func(func(T1) func(T2) T.Tuple2[T1, T2]) func(HKT_T1) HKT_F_T2,
	AP1 ~func(HKT_T2) func(HKT_F_T2) HKT_TUPLE2,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	A1, T1,
	A2, T2,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_F_T2, // HKT[func(T2) T.Tuple2[T1, T2]]
	HKT_TUPLE2 any, // HKT[Tuple2[T1, T2]]
](
	fmap MAP,
	fap1 AP1,
	f1 F1,
	f2 F2,
	t T.Tuple2[A1, A2],
) HKT_TUPLE2 {
	return F.Pipe2(
		f1(t.F1),
		fmap(tupleConstructor2[T1, T2]()),
		fap1(f2(t.F2)),
	)
}

// tupleConstructor3 returns a curried version of [T.MakeTuple3]
func tupleConstructor3[T1, T2, T3 any]() func(T1) func(T2) func(T3) T.Tuple3[T1, T2, T3] {
	return F.Curry3(T.MakeTuple3[T1, T2, T3])
}

// SequenceT3 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 3 higher higher kinded types and returns a higher kinded type of a [Tuple3] with the resolved values.
func SequenceT3[
	MAP ~func(func(T1) func(T2) func(T3) T.Tuple3[T1, T2, T3]) func(HKT_T1) HKT_F_T2_T3,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3) HKT_F_T3,
	AP2 ~func(HKT_T3) func(HKT_F_T3) HKT_TUPLE3,
	T1,
	T2,
	T3,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_F_T2_T3, // HKT[func(T2) func(T3) T.Tuple3[T1, T2, T3]]
	HKT_F_T3, // HKT[func(T3) T.Tuple3[T1, T2, T3]]
	HKT_TUPLE3 any, // HKT[Tuple3[T1, T2, T3]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
) HKT_TUPLE3 {
	return F.Pipe3(
		t1,
		fmap(tupleConstructor3[T1, T2, T3]()),
		fap1(t2),
		fap2(t3),
	)
}

// SequenceTuple3 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple3] of higher higher kinded types and returns a higher kinded type of a [Tuple3] with the resolved values.
func SequenceTuple3[
	MAP ~func(func(T1) func(T2) func(T3) T.Tuple3[T1, T2, T3]) func(HKT_T1) HKT_F_T2_T3,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3) HKT_F_T3,
	AP2 ~func(HKT_T3) func(HKT_F_T3) HKT_TUPLE3,
	T1,
	T2,
	T3,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_F_T2_T3, // HKT[func(T2) func(T3) T.Tuple3[T1, T2, T3]]
	HKT_F_T3, // HKT[func(T3) T.Tuple3[T1, T2, T3]]
	HKT_TUPLE3 any, // HKT[Tuple3[T1, T2, T3]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	t T.Tuple3[HKT_T1, HKT_T2, HKT_T3],
) HKT_TUPLE3 {
	return F.Pipe3(
		t.F1,
		fmap(tupleConstructor3[T1, T2, T3]()),
		fap1(t.F2),
		fap2(t.F3),
	)
}

// TraverseTuple3 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple3] of base types and 3 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple3] with the resolved values.
func TraverseTuple3[
	MAP ~func(func(T1) func(T2) func(T3) T.Tuple3[T1, T2, T3]) func(HKT_T1) HKT_F_T2_T3,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3) HKT_F_T3,
	AP2 ~func(HKT_T3) func(HKT_F_T3) HKT_TUPLE3,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	A1, T1,
	A2, T2,
	A3, T3,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_F_T2_T3, // HKT[func(T2) func(T3) T.Tuple3[T1, T2, T3]]
	HKT_F_T3, // HKT[func(T3) T.Tuple3[T1, T2, T3]]
	HKT_TUPLE3 any, // HKT[Tuple3[T1, T2, T3]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	f1 F1,
	f2 F2,
	f3 F3,
	t T.Tuple3[A1, A2, A3],
) HKT_TUPLE3 {
	return F.Pipe3(
		f1(t.F1),
		fmap(tupleConstructor3[T1, T2, T3]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
	)
}

// tupleConstructor4 returns a curried version of [T.MakeTuple4]
func tupleConstructor4[T1, T2, T3, T4 any]() func(T1) func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4] {
	return F.Curry4(T.MakeTuple4[T1, T2, T3, T4])
}

// SequenceT4 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 4 higher higher kinded types and returns a higher kinded type of a [Tuple4] with the resolved values.
func SequenceT4[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]) func(HKT_T1) HKT_F_T2_T3_T4,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4) HKT_F_T3_T4,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4) HKT_F_T4,
	AP3 ~func(HKT_T4) func(HKT_F_T4) HKT_TUPLE4,
	T1,
	T2,
	T3,
	T4,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_F_T2_T3_T4, // HKT[func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T3_T4, // HKT[func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T4, // HKT[func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_TUPLE4 any, // HKT[Tuple4[T1, T2, T3, T4]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
) HKT_TUPLE4 {
	return F.Pipe4(
		t1,
		fmap(tupleConstructor4[T1, T2, T3, T4]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
	)
}

// SequenceTuple4 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple4] of higher higher kinded types and returns a higher kinded type of a [Tuple4] with the resolved values.
func SequenceTuple4[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]) func(HKT_T1) HKT_F_T2_T3_T4,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4) HKT_F_T3_T4,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4) HKT_F_T4,
	AP3 ~func(HKT_T4) func(HKT_F_T4) HKT_TUPLE4,
	T1,
	T2,
	T3,
	T4,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_F_T2_T3_T4, // HKT[func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T3_T4, // HKT[func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T4, // HKT[func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_TUPLE4 any, // HKT[Tuple4[T1, T2, T3, T4]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	t T.Tuple4[HKT_T1, HKT_T2, HKT_T3, HKT_T4],
) HKT_TUPLE4 {
	return F.Pipe4(
		t.F1,
		fmap(tupleConstructor4[T1, T2, T3, T4]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
	)
}

// TraverseTuple4 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple4] of base types and 4 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple4] with the resolved values.
func TraverseTuple4[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]) func(HKT_T1) HKT_F_T2_T3_T4,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4) HKT_F_T3_T4,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4) HKT_F_T4,
	AP3 ~func(HKT_T4) func(HKT_F_T4) HKT_TUPLE4,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_F_T2_T3_T4, // HKT[func(T2) func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T3_T4, // HKT[func(T3) func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_F_T4, // HKT[func(T4) T.Tuple4[T1, T2, T3, T4]]
	HKT_TUPLE4 any, // HKT[Tuple4[T1, T2, T3, T4]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	t T.Tuple4[A1, A2, A3, A4],
) HKT_TUPLE4 {
	return F.Pipe4(
		f1(t.F1),
		fmap(tupleConstructor4[T1, T2, T3, T4]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
	)
}

// tupleConstructor5 returns a curried version of [T.MakeTuple5]
func tupleConstructor5[T1, T2, T3, T4, T5 any]() func(T1) func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5] {
	return F.Curry5(T.MakeTuple5[T1, T2, T3, T4, T5])
}

// SequenceT5 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 5 higher higher kinded types and returns a higher kinded type of a [Tuple5] with the resolved values.
func SequenceT5[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]) func(HKT_T1) HKT_F_T2_T3_T4_T5,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5) HKT_F_T3_T4_T5,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5) HKT_F_T4_T5,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5) HKT_F_T5,
	AP4 ~func(HKT_T5) func(HKT_F_T5) HKT_TUPLE5,
	T1,
	T2,
	T3,
	T4,
	T5,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_F_T2_T3_T4_T5, // HKT[func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T3_T4_T5, // HKT[func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T4_T5, // HKT[func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T5, // HKT[func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_TUPLE5 any, // HKT[Tuple5[T1, T2, T3, T4, T5]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
) HKT_TUPLE5 {
	return F.Pipe5(
		t1,
		fmap(tupleConstructor5[T1, T2, T3, T4, T5]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
	)
}

// SequenceTuple5 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple5] of higher higher kinded types and returns a higher kinded type of a [Tuple5] with the resolved values.
func SequenceTuple5[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]) func(HKT_T1) HKT_F_T2_T3_T4_T5,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5) HKT_F_T3_T4_T5,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5) HKT_F_T4_T5,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5) HKT_F_T5,
	AP4 ~func(HKT_T5) func(HKT_F_T5) HKT_TUPLE5,
	T1,
	T2,
	T3,
	T4,
	T5,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_F_T2_T3_T4_T5, // HKT[func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T3_T4_T5, // HKT[func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T4_T5, // HKT[func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T5, // HKT[func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_TUPLE5 any, // HKT[Tuple5[T1, T2, T3, T4, T5]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	t T.Tuple5[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5],
) HKT_TUPLE5 {
	return F.Pipe5(
		t.F1,
		fmap(tupleConstructor5[T1, T2, T3, T4, T5]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
	)
}

// TraverseTuple5 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple5] of base types and 5 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple5] with the resolved values.
func TraverseTuple5[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]) func(HKT_T1) HKT_F_T2_T3_T4_T5,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5) HKT_F_T3_T4_T5,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5) HKT_F_T4_T5,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5) HKT_F_T5,
	AP4 ~func(HKT_T5) func(HKT_F_T5) HKT_TUPLE5,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_F_T2_T3_T4_T5, // HKT[func(T2) func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T3_T4_T5, // HKT[func(T3) func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T4_T5, // HKT[func(T4) func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_F_T5, // HKT[func(T5) T.Tuple5[T1, T2, T3, T4, T5]]
	HKT_TUPLE5 any, // HKT[Tuple5[T1, T2, T3, T4, T5]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	t T.Tuple5[A1, A2, A3, A4, A5],
) HKT_TUPLE5 {
	return F.Pipe5(
		f1(t.F1),
		fmap(tupleConstructor5[T1, T2, T3, T4, T5]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
	)
}

// tupleConstructor6 returns a curried version of [T.MakeTuple6]
func tupleConstructor6[T1, T2, T3, T4, T5, T6 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6] {
	return F.Curry6(T.MakeTuple6[T1, T2, T3, T4, T5, T6])
}

// SequenceT6 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 6 higher higher kinded types and returns a higher kinded type of a [Tuple6] with the resolved values.
func SequenceT6[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6) HKT_F_T3_T4_T5_T6,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6) HKT_F_T4_T5_T6,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6) HKT_F_T5_T6,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6) HKT_F_T6,
	AP5 ~func(HKT_T6) func(HKT_F_T6) HKT_TUPLE6,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_F_T2_T3_T4_T5_T6, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T3_T4_T5_T6, // HKT[func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T4_T5_T6, // HKT[func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T5_T6, // HKT[func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T6, // HKT[func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_TUPLE6 any, // HKT[Tuple6[T1, T2, T3, T4, T5, T6]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
) HKT_TUPLE6 {
	return F.Pipe6(
		t1,
		fmap(tupleConstructor6[T1, T2, T3, T4, T5, T6]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
	)
}

// SequenceTuple6 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple6] of higher higher kinded types and returns a higher kinded type of a [Tuple6] with the resolved values.
func SequenceTuple6[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6) HKT_F_T3_T4_T5_T6,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6) HKT_F_T4_T5_T6,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6) HKT_F_T5_T6,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6) HKT_F_T6,
	AP5 ~func(HKT_T6) func(HKT_F_T6) HKT_TUPLE6,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_F_T2_T3_T4_T5_T6, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T3_T4_T5_T6, // HKT[func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T4_T5_T6, // HKT[func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T5_T6, // HKT[func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T6, // HKT[func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_TUPLE6 any, // HKT[Tuple6[T1, T2, T3, T4, T5, T6]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	t T.Tuple6[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6],
) HKT_TUPLE6 {
	return F.Pipe6(
		t.F1,
		fmap(tupleConstructor6[T1, T2, T3, T4, T5, T6]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
	)
}

// TraverseTuple6 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple6] of base types and 6 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple6] with the resolved values.
func TraverseTuple6[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6) HKT_F_T3_T4_T5_T6,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6) HKT_F_T4_T5_T6,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6) HKT_F_T5_T6,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6) HKT_F_T6,
	AP5 ~func(HKT_T6) func(HKT_F_T6) HKT_TUPLE6,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_F_T2_T3_T4_T5_T6, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T3_T4_T5_T6, // HKT[func(T3) func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T4_T5_T6, // HKT[func(T4) func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T5_T6, // HKT[func(T5) func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_F_T6, // HKT[func(T6) T.Tuple6[T1, T2, T3, T4, T5, T6]]
	HKT_TUPLE6 any, // HKT[Tuple6[T1, T2, T3, T4, T5, T6]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	t T.Tuple6[A1, A2, A3, A4, A5, A6],
) HKT_TUPLE6 {
	return F.Pipe6(
		f1(t.F1),
		fmap(tupleConstructor6[T1, T2, T3, T4, T5, T6]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
	)
}

// tupleConstructor7 returns a curried version of [T.MakeTuple7]
func tupleConstructor7[T1, T2, T3, T4, T5, T6, T7 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7] {
	return F.Curry7(T.MakeTuple7[T1, T2, T3, T4, T5, T6, T7])
}

// SequenceT7 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 7 higher higher kinded types and returns a higher kinded type of a [Tuple7] with the resolved values.
func SequenceT7[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7) HKT_F_T3_T4_T5_T6_T7,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7) HKT_F_T4_T5_T6_T7,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7) HKT_F_T5_T6_T7,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7) HKT_F_T6_T7,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7) HKT_F_T7,
	AP6 ~func(HKT_T7) func(HKT_F_T7) HKT_TUPLE7,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_F_T2_T3_T4_T5_T6_T7, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T3_T4_T5_T6_T7, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T4_T5_T6_T7, // HKT[func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T5_T6_T7, // HKT[func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T6_T7, // HKT[func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T7, // HKT[func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_TUPLE7 any, // HKT[Tuple7[T1, T2, T3, T4, T5, T6, T7]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
) HKT_TUPLE7 {
	return F.Pipe7(
		t1,
		fmap(tupleConstructor7[T1, T2, T3, T4, T5, T6, T7]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
	)
}

// SequenceTuple7 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple7] of higher higher kinded types and returns a higher kinded type of a [Tuple7] with the resolved values.
func SequenceTuple7[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7) HKT_F_T3_T4_T5_T6_T7,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7) HKT_F_T4_T5_T6_T7,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7) HKT_F_T5_T6_T7,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7) HKT_F_T6_T7,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7) HKT_F_T7,
	AP6 ~func(HKT_T7) func(HKT_F_T7) HKT_TUPLE7,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_F_T2_T3_T4_T5_T6_T7, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T3_T4_T5_T6_T7, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T4_T5_T6_T7, // HKT[func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T5_T6_T7, // HKT[func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T6_T7, // HKT[func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T7, // HKT[func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_TUPLE7 any, // HKT[Tuple7[T1, T2, T3, T4, T5, T6, T7]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	t T.Tuple7[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7],
) HKT_TUPLE7 {
	return F.Pipe7(
		t.F1,
		fmap(tupleConstructor7[T1, T2, T3, T4, T5, T6, T7]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
	)
}

// TraverseTuple7 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple7] of base types and 7 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple7] with the resolved values.
func TraverseTuple7[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7) HKT_F_T3_T4_T5_T6_T7,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7) HKT_F_T4_T5_T6_T7,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7) HKT_F_T5_T6_T7,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7) HKT_F_T6_T7,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7) HKT_F_T7,
	AP6 ~func(HKT_T7) func(HKT_F_T7) HKT_TUPLE7,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_F_T2_T3_T4_T5_T6_T7, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T3_T4_T5_T6_T7, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T4_T5_T6_T7, // HKT[func(T4) func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T5_T6_T7, // HKT[func(T5) func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T6_T7, // HKT[func(T6) func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_F_T7, // HKT[func(T7) T.Tuple7[T1, T2, T3, T4, T5, T6, T7]]
	HKT_TUPLE7 any, // HKT[Tuple7[T1, T2, T3, T4, T5, T6, T7]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	t T.Tuple7[A1, A2, A3, A4, A5, A6, A7],
) HKT_TUPLE7 {
	return F.Pipe7(
		f1(t.F1),
		fmap(tupleConstructor7[T1, T2, T3, T4, T5, T6, T7]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
	)
}

// tupleConstructor8 returns a curried version of [T.MakeTuple8]
func tupleConstructor8[T1, T2, T3, T4, T5, T6, T7, T8 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8] {
	return F.Curry8(T.MakeTuple8[T1, T2, T3, T4, T5, T6, T7, T8])
}

// SequenceT8 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 8 higher higher kinded types and returns a higher kinded type of a [Tuple8] with the resolved values.
func SequenceT8[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8) HKT_F_T3_T4_T5_T6_T7_T8,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8) HKT_F_T4_T5_T6_T7_T8,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8) HKT_F_T5_T6_T7_T8,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8) HKT_F_T6_T7_T8,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8) HKT_F_T7_T8,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8) HKT_F_T8,
	AP7 ~func(HKT_T8) func(HKT_F_T8) HKT_TUPLE8,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_F_T2_T3_T4_T5_T6_T7_T8, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T3_T4_T5_T6_T7_T8, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T4_T5_T6_T7_T8, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T5_T6_T7_T8, // HKT[func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T6_T7_T8, // HKT[func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T7_T8, // HKT[func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T8, // HKT[func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_TUPLE8 any, // HKT[Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
) HKT_TUPLE8 {
	return F.Pipe8(
		t1,
		fmap(tupleConstructor8[T1, T2, T3, T4, T5, T6, T7, T8]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
	)
}

// SequenceTuple8 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple8] of higher higher kinded types and returns a higher kinded type of a [Tuple8] with the resolved values.
func SequenceTuple8[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8) HKT_F_T3_T4_T5_T6_T7_T8,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8) HKT_F_T4_T5_T6_T7_T8,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8) HKT_F_T5_T6_T7_T8,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8) HKT_F_T6_T7_T8,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8) HKT_F_T7_T8,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8) HKT_F_T8,
	AP7 ~func(HKT_T8) func(HKT_F_T8) HKT_TUPLE8,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_F_T2_T3_T4_T5_T6_T7_T8, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T3_T4_T5_T6_T7_T8, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T4_T5_T6_T7_T8, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T5_T6_T7_T8, // HKT[func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T6_T7_T8, // HKT[func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T7_T8, // HKT[func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T8, // HKT[func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_TUPLE8 any, // HKT[Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	t T.Tuple8[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8],
) HKT_TUPLE8 {
	return F.Pipe8(
		t.F1,
		fmap(tupleConstructor8[T1, T2, T3, T4, T5, T6, T7, T8]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
	)
}

// TraverseTuple8 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple8] of base types and 8 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple8] with the resolved values.
func TraverseTuple8[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8) HKT_F_T3_T4_T5_T6_T7_T8,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8) HKT_F_T4_T5_T6_T7_T8,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8) HKT_F_T5_T6_T7_T8,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8) HKT_F_T6_T7_T8,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8) HKT_F_T7_T8,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8) HKT_F_T8,
	AP7 ~func(HKT_T8) func(HKT_F_T8) HKT_TUPLE8,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_F_T2_T3_T4_T5_T6_T7_T8, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T3_T4_T5_T6_T7_T8, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T4_T5_T6_T7_T8, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T5_T6_T7_T8, // HKT[func(T5) func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T6_T7_T8, // HKT[func(T6) func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T7_T8, // HKT[func(T7) func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_F_T8, // HKT[func(T8) T.Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
	HKT_TUPLE8 any, // HKT[Tuple8[T1, T2, T3, T4, T5, T6, T7, T8]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	t T.Tuple8[A1, A2, A3, A4, A5, A6, A7, A8],
) HKT_TUPLE8 {
	return F.Pipe8(
		f1(t.F1),
		fmap(tupleConstructor8[T1, T2, T3, T4, T5, T6, T7, T8]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
	)
}

// tupleConstructor9 returns a curried version of [T.MakeTuple9]
func tupleConstructor9[T1, T2, T3, T4, T5, T6, T7, T8, T9 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9] {
	return F.Curry9(T.MakeTuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9])
}

// SequenceT9 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 9 higher higher kinded types and returns a higher kinded type of a [Tuple9] with the resolved values.
func SequenceT9[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9) HKT_F_T3_T4_T5_T6_T7_T8_T9,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9) HKT_F_T4_T5_T6_T7_T8_T9,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9) HKT_F_T5_T6_T7_T8_T9,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9) HKT_F_T6_T7_T8_T9,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9) HKT_F_T7_T8_T9,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9) HKT_F_T8_T9,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9) HKT_F_T9,
	AP8 ~func(HKT_T9) func(HKT_F_T9) HKT_TUPLE9,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T4_T5_T6_T7_T8_T9, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T5_T6_T7_T8_T9, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T6_T7_T8_T9, // HKT[func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T7_T8_T9, // HKT[func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T8_T9, // HKT[func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T9, // HKT[func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_TUPLE9 any, // HKT[Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
) HKT_TUPLE9 {
	return F.Pipe9(
		t1,
		fmap(tupleConstructor9[T1, T2, T3, T4, T5, T6, T7, T8, T9]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
	)
}

// SequenceTuple9 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple9] of higher higher kinded types and returns a higher kinded type of a [Tuple9] with the resolved values.
func SequenceTuple9[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9) HKT_F_T3_T4_T5_T6_T7_T8_T9,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9) HKT_F_T4_T5_T6_T7_T8_T9,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9) HKT_F_T5_T6_T7_T8_T9,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9) HKT_F_T6_T7_T8_T9,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9) HKT_F_T7_T8_T9,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9) HKT_F_T8_T9,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9) HKT_F_T9,
	AP8 ~func(HKT_T9) func(HKT_F_T9) HKT_TUPLE9,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T4_T5_T6_T7_T8_T9, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T5_T6_T7_T8_T9, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T6_T7_T8_T9, // HKT[func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T7_T8_T9, // HKT[func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T8_T9, // HKT[func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T9, // HKT[func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_TUPLE9 any, // HKT[Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	t T.Tuple9[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9],
) HKT_TUPLE9 {
	return F.Pipe9(
		t.F1,
		fmap(tupleConstructor9[T1, T2, T3, T4, T5, T6, T7, T8, T9]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
	)
}

// TraverseTuple9 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple9] of base types and 9 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple9] with the resolved values.
func TraverseTuple9[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9) HKT_F_T3_T4_T5_T6_T7_T8_T9,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9) HKT_F_T4_T5_T6_T7_T8_T9,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9) HKT_F_T5_T6_T7_T8_T9,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9) HKT_F_T6_T7_T8_T9,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9) HKT_F_T7_T8_T9,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9) HKT_F_T8_T9,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9) HKT_F_T9,
	AP8 ~func(HKT_T9) func(HKT_F_T9) HKT_TUPLE9,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T4_T5_T6_T7_T8_T9, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T5_T6_T7_T8_T9, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T6_T7_T8_T9, // HKT[func(T6) func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T7_T8_T9, // HKT[func(T7) func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T8_T9, // HKT[func(T8) func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_F_T9, // HKT[func(T9) T.Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
	HKT_TUPLE9 any, // HKT[Tuple9[T1, T2, T3, T4, T5, T6, T7, T8, T9]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	t T.Tuple9[A1, A2, A3, A4, A5, A6, A7, A8, A9],
) HKT_TUPLE9 {
	return F.Pipe9(
		f1(t.F1),
		fmap(tupleConstructor9[T1, T2, T3, T4, T5, T6, T7, T8, T9]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
	)
}

// tupleConstructor10 returns a curried version of [T.MakeTuple10]
func tupleConstructor10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10] {
	return F.Curry10(T.MakeTuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10])
}

// SequenceT10 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 10 higher higher kinded types and returns a higher kinded type of a [Tuple10] with the resolved values.
func SequenceT10[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T4_T5_T6_T7_T8_T9_T10,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10) HKT_F_T5_T6_T7_T8_T9_T10,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10) HKT_F_T6_T7_T8_T9_T10,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10) HKT_F_T7_T8_T9_T10,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10) HKT_F_T8_T9_T10,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10) HKT_F_T9_T10,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10) HKT_F_T10,
	AP9 ~func(HKT_T10) func(HKT_F_T10) HKT_TUPLE10,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T5_T6_T7_T8_T9_T10, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T6_T7_T8_T9_T10, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T7_T8_T9_T10, // HKT[func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T8_T9_T10, // HKT[func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T9_T10, // HKT[func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T10, // HKT[func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_TUPLE10 any, // HKT[Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
) HKT_TUPLE10 {
	return F.Pipe10(
		t1,
		fmap(tupleConstructor10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
	)
}

// SequenceTuple10 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple10] of higher higher kinded types and returns a higher kinded type of a [Tuple10] with the resolved values.
func SequenceTuple10[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T4_T5_T6_T7_T8_T9_T10,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10) HKT_F_T5_T6_T7_T8_T9_T10,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10) HKT_F_T6_T7_T8_T9_T10,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10) HKT_F_T7_T8_T9_T10,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10) HKT_F_T8_T9_T10,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10) HKT_F_T9_T10,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10) HKT_F_T10,
	AP9 ~func(HKT_T10) func(HKT_F_T10) HKT_TUPLE10,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T5_T6_T7_T8_T9_T10, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T6_T7_T8_T9_T10, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T7_T8_T9_T10, // HKT[func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T8_T9_T10, // HKT[func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T9_T10, // HKT[func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T10, // HKT[func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_TUPLE10 any, // HKT[Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	t T.Tuple10[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10],
) HKT_TUPLE10 {
	return F.Pipe10(
		t.F1,
		fmap(tupleConstructor10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
	)
}

// TraverseTuple10 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple10] of base types and 10 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple10] with the resolved values.
func TraverseTuple10[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10) HKT_F_T4_T5_T6_T7_T8_T9_T10,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10) HKT_F_T5_T6_T7_T8_T9_T10,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10) HKT_F_T6_T7_T8_T9_T10,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10) HKT_F_T7_T8_T9_T10,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10) HKT_F_T8_T9_T10,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10) HKT_F_T9_T10,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10) HKT_F_T10,
	AP9 ~func(HKT_T10) func(HKT_F_T10) HKT_TUPLE10,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T5_T6_T7_T8_T9_T10, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T6_T7_T8_T9_T10, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T7_T8_T9_T10, // HKT[func(T7) func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T8_T9_T10, // HKT[func(T8) func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T9_T10, // HKT[func(T9) func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_F_T10, // HKT[func(T10) T.Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
	HKT_TUPLE10 any, // HKT[Tuple10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	t T.Tuple10[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10],
) HKT_TUPLE10 {
	return F.Pipe10(
		f1(t.F1),
		fmap(tupleConstructor10[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
	)
}

// tupleConstructor11 returns a curried version of [T.MakeTuple11]
func tupleConstructor11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11] {
	return F.Curry11(T.MakeTuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11])
}

// SequenceT11 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 11 higher higher kinded types and returns a higher kinded type of a [Tuple11] with the resolved values.
func SequenceT11[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T5_T6_T7_T8_T9_T10_T11,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11) HKT_F_T6_T7_T8_T9_T10_T11,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11) HKT_F_T7_T8_T9_T10_T11,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11) HKT_F_T8_T9_T10_T11,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11) HKT_F_T9_T10_T11,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11) HKT_F_T10_T11,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11) HKT_F_T11,
	AP10 ~func(HKT_T11) func(HKT_F_T11) HKT_TUPLE11,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T6_T7_T8_T9_T10_T11, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T7_T8_T9_T10_T11, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T8_T9_T10_T11, // HKT[func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T9_T10_T11, // HKT[func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T10_T11, // HKT[func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T11, // HKT[func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_TUPLE11 any, // HKT[Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
	t11 HKT_T11,
) HKT_TUPLE11 {
	return F.Pipe11(
		t1,
		fmap(tupleConstructor11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
		fap10(t11),
	)
}

// SequenceTuple11 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple11] of higher higher kinded types and returns a higher kinded type of a [Tuple11] with the resolved values.
func SequenceTuple11[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T5_T6_T7_T8_T9_T10_T11,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11) HKT_F_T6_T7_T8_T9_T10_T11,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11) HKT_F_T7_T8_T9_T10_T11,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11) HKT_F_T8_T9_T10_T11,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11) HKT_F_T9_T10_T11,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11) HKT_F_T10_T11,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11) HKT_F_T11,
	AP10 ~func(HKT_T11) func(HKT_F_T11) HKT_TUPLE11,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T6_T7_T8_T9_T10_T11, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T7_T8_T9_T10_T11, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T8_T9_T10_T11, // HKT[func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T9_T10_T11, // HKT[func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T10_T11, // HKT[func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T11, // HKT[func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_TUPLE11 any, // HKT[Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	t T.Tuple11[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10, HKT_T11],
) HKT_TUPLE11 {
	return F.Pipe11(
		t.F1,
		fmap(tupleConstructor11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
		fap10(t.F11),
	)
}

// TraverseTuple11 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple11] of base types and 11 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple11] with the resolved values.
func TraverseTuple11[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11) HKT_F_T5_T6_T7_T8_T9_T10_T11,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11) HKT_F_T6_T7_T8_T9_T10_T11,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11) HKT_F_T7_T8_T9_T10_T11,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11) HKT_F_T8_T9_T10_T11,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11) HKT_F_T9_T10_T11,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11) HKT_F_T10_T11,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11) HKT_F_T11,
	AP10 ~func(HKT_T11) func(HKT_F_T11) HKT_TUPLE11,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	F11 ~func(A11) HKT_T11,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	A11, T11,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T6_T7_T8_T9_T10_T11, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T7_T8_T9_T10_T11, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T8_T9_T10_T11, // HKT[func(T8) func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T9_T10_T11, // HKT[func(T9) func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T10_T11, // HKT[func(T10) func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_F_T11, // HKT[func(T11) T.Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
	HKT_TUPLE11 any, // HKT[Tuple11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	f11 F11,
	t T.Tuple11[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11],
) HKT_TUPLE11 {
	return F.Pipe11(
		f1(t.F1),
		fmap(tupleConstructor11[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
		fap10(f11(t.F11)),
	)
}

// tupleConstructor12 returns a curried version of [T.MakeTuple12]
func tupleConstructor12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12] {
	return F.Curry12(T.MakeTuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12])
}

// SequenceT12 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 12 higher higher kinded types and returns a higher kinded type of a [Tuple12] with the resolved values.
func SequenceT12[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T6_T7_T8_T9_T10_T11_T12,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12) HKT_F_T7_T8_T9_T10_T11_T12,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12) HKT_F_T8_T9_T10_T11_T12,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12) HKT_F_T9_T10_T11_T12,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12) HKT_F_T10_T11_T12,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12) HKT_F_T11_T12,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12) HKT_F_T12,
	AP11 ~func(HKT_T12) func(HKT_F_T12) HKT_TUPLE12,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T7_T8_T9_T10_T11_T12, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T8_T9_T10_T11_T12, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T9_T10_T11_T12, // HKT[func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T10_T11_T12, // HKT[func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T11_T12, // HKT[func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T12, // HKT[func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_TUPLE12 any, // HKT[Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
	t11 HKT_T11,
	t12 HKT_T12,
) HKT_TUPLE12 {
	return F.Pipe12(
		t1,
		fmap(tupleConstructor12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
		fap10(t11),
		fap11(t12),
	)
}

// SequenceTuple12 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple12] of higher higher kinded types and returns a higher kinded type of a [Tuple12] with the resolved values.
func SequenceTuple12[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T6_T7_T8_T9_T10_T11_T12,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12) HKT_F_T7_T8_T9_T10_T11_T12,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12) HKT_F_T8_T9_T10_T11_T12,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12) HKT_F_T9_T10_T11_T12,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12) HKT_F_T10_T11_T12,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12) HKT_F_T11_T12,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12) HKT_F_T12,
	AP11 ~func(HKT_T12) func(HKT_F_T12) HKT_TUPLE12,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T7_T8_T9_T10_T11_T12, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T8_T9_T10_T11_T12, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T9_T10_T11_T12, // HKT[func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T10_T11_T12, // HKT[func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T11_T12, // HKT[func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T12, // HKT[func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_TUPLE12 any, // HKT[Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	t T.Tuple12[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10, HKT_T11, HKT_T12],
) HKT_TUPLE12 {
	return F.Pipe12(
		t.F1,
		fmap(tupleConstructor12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
		fap10(t.F11),
		fap11(t.F12),
	)
}

// TraverseTuple12 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple12] of base types and 12 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple12] with the resolved values.
func TraverseTuple12[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12) HKT_F_T6_T7_T8_T9_T10_T11_T12,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12) HKT_F_T7_T8_T9_T10_T11_T12,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12) HKT_F_T8_T9_T10_T11_T12,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12) HKT_F_T9_T10_T11_T12,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12) HKT_F_T10_T11_T12,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12) HKT_F_T11_T12,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12) HKT_F_T12,
	AP11 ~func(HKT_T12) func(HKT_F_T12) HKT_TUPLE12,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	F11 ~func(A11) HKT_T11,
	F12 ~func(A12) HKT_T12,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	A11, T11,
	A12, T12,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T7_T8_T9_T10_T11_T12, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T8_T9_T10_T11_T12, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T9_T10_T11_T12, // HKT[func(T9) func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T10_T11_T12, // HKT[func(T10) func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T11_T12, // HKT[func(T11) func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_F_T12, // HKT[func(T12) T.Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
	HKT_TUPLE12 any, // HKT[Tuple12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	f11 F11,
	f12 F12,
	t T.Tuple12[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11, A12],
) HKT_TUPLE12 {
	return F.Pipe12(
		f1(t.F1),
		fmap(tupleConstructor12[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
		fap10(f11(t.F11)),
		fap11(f12(t.F12)),
	)
}

// tupleConstructor13 returns a curried version of [T.MakeTuple13]
func tupleConstructor13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13] {
	return F.Curry13(T.MakeTuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13])
}

// SequenceT13 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 13 higher higher kinded types and returns a higher kinded type of a [Tuple13] with the resolved values.
func SequenceT13[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T7_T8_T9_T10_T11_T12_T13,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13) HKT_F_T8_T9_T10_T11_T12_T13,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13) HKT_F_T9_T10_T11_T12_T13,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13) HKT_F_T10_T11_T12_T13,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13) HKT_F_T11_T12_T13,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13) HKT_F_T12_T13,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13) HKT_F_T13,
	AP12 ~func(HKT_T13) func(HKT_F_T13) HKT_TUPLE13,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T8_T9_T10_T11_T12_T13, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T9_T10_T11_T12_T13, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T10_T11_T12_T13, // HKT[func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T11_T12_T13, // HKT[func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T12_T13, // HKT[func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T13, // HKT[func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_TUPLE13 any, // HKT[Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
	t11 HKT_T11,
	t12 HKT_T12,
	t13 HKT_T13,
) HKT_TUPLE13 {
	return F.Pipe13(
		t1,
		fmap(tupleConstructor13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
		fap10(t11),
		fap11(t12),
		fap12(t13),
	)
}

// SequenceTuple13 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple13] of higher higher kinded types and returns a higher kinded type of a [Tuple13] with the resolved values.
func SequenceTuple13[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T7_T8_T9_T10_T11_T12_T13,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13) HKT_F_T8_T9_T10_T11_T12_T13,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13) HKT_F_T9_T10_T11_T12_T13,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13) HKT_F_T10_T11_T12_T13,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13) HKT_F_T11_T12_T13,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13) HKT_F_T12_T13,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13) HKT_F_T13,
	AP12 ~func(HKT_T13) func(HKT_F_T13) HKT_TUPLE13,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T8_T9_T10_T11_T12_T13, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T9_T10_T11_T12_T13, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T10_T11_T12_T13, // HKT[func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T11_T12_T13, // HKT[func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T12_T13, // HKT[func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T13, // HKT[func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_TUPLE13 any, // HKT[Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	t T.Tuple13[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10, HKT_T11, HKT_T12, HKT_T13],
) HKT_TUPLE13 {
	return F.Pipe13(
		t.F1,
		fmap(tupleConstructor13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
		fap10(t.F11),
		fap11(t.F12),
		fap12(t.F13),
	)
}

// TraverseTuple13 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple13] of base types and 13 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple13] with the resolved values.
func TraverseTuple13[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13) HKT_F_T7_T8_T9_T10_T11_T12_T13,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13) HKT_F_T8_T9_T10_T11_T12_T13,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13) HKT_F_T9_T10_T11_T12_T13,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13) HKT_F_T10_T11_T12_T13,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13) HKT_F_T11_T12_T13,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13) HKT_F_T12_T13,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13) HKT_F_T13,
	AP12 ~func(HKT_T13) func(HKT_F_T13) HKT_TUPLE13,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	F11 ~func(A11) HKT_T11,
	F12 ~func(A12) HKT_T12,
	F13 ~func(A13) HKT_T13,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	A11, T11,
	A12, T12,
	A13, T13,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T8_T9_T10_T11_T12_T13, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T9_T10_T11_T12_T13, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T10_T11_T12_T13, // HKT[func(T10) func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T11_T12_T13, // HKT[func(T11) func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T12_T13, // HKT[func(T12) func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_F_T13, // HKT[func(T13) T.Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
	HKT_TUPLE13 any, // HKT[Tuple13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	f11 F11,
	f12 F12,
	f13 F13,
	t T.Tuple13[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11, A12, A13],
) HKT_TUPLE13 {
	return F.Pipe13(
		f1(t.F1),
		fmap(tupleConstructor13[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
		fap10(f11(t.F11)),
		fap11(f12(t.F12)),
		fap12(f13(t.F13)),
	)
}

// tupleConstructor14 returns a curried version of [T.MakeTuple14]
func tupleConstructor14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14] {
	return F.Curry14(T.MakeTuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14])
}

// SequenceT14 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 14 higher higher kinded types and returns a higher kinded type of a [Tuple14] with the resolved values.
func SequenceT14[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T8_T9_T10_T11_T12_T13_T14,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14) HKT_F_T9_T10_T11_T12_T13_T14,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14) HKT_F_T10_T11_T12_T13_T14,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14) HKT_F_T11_T12_T13_T14,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14) HKT_F_T12_T13_T14,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14) HKT_F_T13_T14,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14) HKT_F_T14,
	AP13 ~func(HKT_T14) func(HKT_F_T14) HKT_TUPLE14,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	T14,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T9_T10_T11_T12_T13_T14, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T10_T11_T12_T13_T14, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T11_T12_T13_T14, // HKT[func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T12_T13_T14, // HKT[func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T13_T14, // HKT[func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T14, // HKT[func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_TUPLE14 any, // HKT[Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
	t11 HKT_T11,
	t12 HKT_T12,
	t13 HKT_T13,
	t14 HKT_T14,
) HKT_TUPLE14 {
	return F.Pipe14(
		t1,
		fmap(tupleConstructor14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
		fap10(t11),
		fap11(t12),
		fap12(t13),
		fap13(t14),
	)
}

// SequenceTuple14 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple14] of higher higher kinded types and returns a higher kinded type of a [Tuple14] with the resolved values.
func SequenceTuple14[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T8_T9_T10_T11_T12_T13_T14,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14) HKT_F_T9_T10_T11_T12_T13_T14,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14) HKT_F_T10_T11_T12_T13_T14,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14) HKT_F_T11_T12_T13_T14,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14) HKT_F_T12_T13_T14,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14) HKT_F_T13_T14,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14) HKT_F_T14,
	AP13 ~func(HKT_T14) func(HKT_F_T14) HKT_TUPLE14,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	T14,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T9_T10_T11_T12_T13_T14, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T10_T11_T12_T13_T14, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T11_T12_T13_T14, // HKT[func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T12_T13_T14, // HKT[func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T13_T14, // HKT[func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T14, // HKT[func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_TUPLE14 any, // HKT[Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	t T.Tuple14[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10, HKT_T11, HKT_T12, HKT_T13, HKT_T14],
) HKT_TUPLE14 {
	return F.Pipe14(
		t.F1,
		fmap(tupleConstructor14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
		fap10(t.F11),
		fap11(t.F12),
		fap12(t.F13),
		fap13(t.F14),
	)
}

// TraverseTuple14 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple14] of base types and 14 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple14] with the resolved values.
func TraverseTuple14[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14) HKT_F_T8_T9_T10_T11_T12_T13_T14,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14) HKT_F_T9_T10_T11_T12_T13_T14,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14) HKT_F_T10_T11_T12_T13_T14,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14) HKT_F_T11_T12_T13_T14,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14) HKT_F_T12_T13_T14,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14) HKT_F_T13_T14,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14) HKT_F_T14,
	AP13 ~func(HKT_T14) func(HKT_F_T14) HKT_TUPLE14,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	F11 ~func(A11) HKT_T11,
	F12 ~func(A12) HKT_T12,
	F13 ~func(A13) HKT_T13,
	F14 ~func(A14) HKT_T14,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	A11, T11,
	A12, T12,
	A13, T13,
	A14, T14,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T9_T10_T11_T12_T13_T14, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T10_T11_T12_T13_T14, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T11_T12_T13_T14, // HKT[func(T11) func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T12_T13_T14, // HKT[func(T12) func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T13_T14, // HKT[func(T13) func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_F_T14, // HKT[func(T14) T.Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
	HKT_TUPLE14 any, // HKT[Tuple14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	f11 F11,
	f12 F12,
	f13 F13,
	f14 F14,
	t T.Tuple14[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11, A12, A13, A14],
) HKT_TUPLE14 {
	return F.Pipe14(
		f1(t.F1),
		fmap(tupleConstructor14[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
		fap10(f11(t.F11)),
		fap11(f12(t.F12)),
		fap12(f13(t.F13)),
		fap13(f14(t.F14)),
	)
}

// tupleConstructor15 returns a curried version of [T.MakeTuple15]
func tupleConstructor15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15 any]() func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15] {
	return F.Curry15(T.MakeTuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15])
}

// SequenceT15 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes 15 higher higher kinded types and returns a higher kinded type of a [Tuple15] with the resolved values.
func SequenceT15[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T8_T9_T10_T11_T12_T13_T14_T15,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T9_T10_T11_T12_T13_T14_T15,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14_T15) HKT_F_T10_T11_T12_T13_T14_T15,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14_T15) HKT_F_T11_T12_T13_T14_T15,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14_T15) HKT_F_T12_T13_T14_T15,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14_T15) HKT_F_T13_T14_T15,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14_T15) HKT_F_T14_T15,
	AP13 ~func(HKT_T14) func(HKT_F_T14_T15) HKT_F_T15,
	AP14 ~func(HKT_T15) func(HKT_F_T15) HKT_TUPLE15,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	T14,
	T15,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_T15, // HKT[T15]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T10_T11_T12_T13_T14_T15, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T11_T12_T13_T14_T15, // HKT[func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T12_T13_T14_T15, // HKT[func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T13_T14_T15, // HKT[func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T14_T15, // HKT[func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T15, // HKT[func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_TUPLE15 any, // HKT[Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	fap14 AP14,
	t1 HKT_T1,
	t2 HKT_T2,
	t3 HKT_T3,
	t4 HKT_T4,
	t5 HKT_T5,
	t6 HKT_T6,
	t7 HKT_T7,
	t8 HKT_T8,
	t9 HKT_T9,
	t10 HKT_T10,
	t11 HKT_T11,
	t12 HKT_T12,
	t13 HKT_T13,
	t14 HKT_T14,
	t15 HKT_T15,
) HKT_TUPLE15 {
	return F.Pipe15(
		t1,
		fmap(tupleConstructor15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]()),
		fap1(t2),
		fap2(t3),
		fap3(t4),
		fap4(t5),
		fap5(t6),
		fap6(t7),
		fap7(t8),
		fap8(t9),
		fap9(t10),
		fap10(t11),
		fap11(t12),
		fap12(t13),
		fap13(t14),
		fap14(t15),
	)
}

// SequenceTuple15 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple15] of higher higher kinded types and returns a higher kinded type of a [Tuple15] with the resolved values.
func SequenceTuple15[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T8_T9_T10_T11_T12_T13_T14_T15,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T9_T10_T11_T12_T13_T14_T15,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14_T15) HKT_F_T10_T11_T12_T13_T14_T15,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14_T15) HKT_F_T11_T12_T13_T14_T15,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14_T15) HKT_F_T12_T13_T14_T15,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14_T15) HKT_F_T13_T14_T15,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14_T15) HKT_F_T14_T15,
	AP13 ~func(HKT_T14) func(HKT_F_T14_T15) HKT_F_T15,
	AP14 ~func(HKT_T15) func(HKT_F_T15) HKT_TUPLE15,
	T1,
	T2,
	T3,
	T4,
	T5,
	T6,
	T7,
	T8,
	T9,
	T10,
	T11,
	T12,
	T13,
	T14,
	T15,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_T15, // HKT[T15]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T10_T11_T12_T13_T14_T15, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T11_T12_T13_T14_T15, // HKT[func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T12_T13_T14_T15, // HKT[func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T13_T14_T15, // HKT[func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T14_T15, // HKT[func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T15, // HKT[func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_TUPLE15 any, // HKT[Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	fap14 AP14,
	t T.Tuple15[HKT_T1, HKT_T2, HKT_T3, HKT_T4, HKT_T5, HKT_T6, HKT_T7, HKT_T8, HKT_T9, HKT_T10, HKT_T11, HKT_T12, HKT_T13, HKT_T14, HKT_T15],
) HKT_TUPLE15 {
	return F.Pipe15(
		t.F1,
		fmap(tupleConstructor15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]()),
		fap1(t.F2),
		fap2(t.F3),
		fap3(t.F4),
		fap4(t.F5),
		fap5(t.F6),
		fap6(t.F7),
		fap7(t.F8),
		fap8(t.F9),
		fap9(t.F10),
		fap10(t.F11),
		fap11(t.F12),
		fap12(t.F13),
		fap13(t.F14),
		fap14(t.F15),
	)
}

// TraverseTuple15 is a utility function used to implement the sequence operation for higher kinded types based only on map and ap.
// The function takes a [Tuple15] of base types and 15 functions that transform these based types into higher higher kinded types. It returns a higher kinded type of a [Tuple15] with the resolved values.
func TraverseTuple15[
	MAP ~func(func(T1) func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]) func(HKT_T1) HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP1 ~func(HKT_T2) func(HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP2 ~func(HKT_T3) func(HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP3 ~func(HKT_T4) func(HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP4 ~func(HKT_T5) func(HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP5 ~func(HKT_T6) func(HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15,
	AP6 ~func(HKT_T7) func(HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T8_T9_T10_T11_T12_T13_T14_T15,
	AP7 ~func(HKT_T8) func(HKT_F_T8_T9_T10_T11_T12_T13_T14_T15) HKT_F_T9_T10_T11_T12_T13_T14_T15,
	AP8 ~func(HKT_T9) func(HKT_F_T9_T10_T11_T12_T13_T14_T15) HKT_F_T10_T11_T12_T13_T14_T15,
	AP9 ~func(HKT_T10) func(HKT_F_T10_T11_T12_T13_T14_T15) HKT_F_T11_T12_T13_T14_T15,
	AP10 ~func(HKT_T11) func(HKT_F_T11_T12_T13_T14_T15) HKT_F_T12_T13_T14_T15,
	AP11 ~func(HKT_T12) func(HKT_F_T12_T13_T14_T15) HKT_F_T13_T14_T15,
	AP12 ~func(HKT_T13) func(HKT_F_T13_T14_T15) HKT_F_T14_T15,
	AP13 ~func(HKT_T14) func(HKT_F_T14_T15) HKT_F_T15,
	AP14 ~func(HKT_T15) func(HKT_F_T15) HKT_TUPLE15,
	F1 ~func(A1) HKT_T1,
	F2 ~func(A2) HKT_T2,
	F3 ~func(A3) HKT_T3,
	F4 ~func(A4) HKT_T4,
	F5 ~func(A5) HKT_T5,
	F6 ~func(A6) HKT_T6,
	F7 ~func(A7) HKT_T7,
	F8 ~func(A8) HKT_T8,
	F9 ~func(A9) HKT_T9,
	F10 ~func(A10) HKT_T10,
	F11 ~func(A11) HKT_T11,
	F12 ~func(A12) HKT_T12,
	F13 ~func(A13) HKT_T13,
	F14 ~func(A14) HKT_T14,
	F15 ~func(A15) HKT_T15,
	A1, T1,
	A2, T2,
	A3, T3,
	A4, T4,
	A5, T5,
	A6, T6,
	A7, T7,
	A8, T8,
	A9, T9,
	A10, T10,
	A11, T11,
	A12, T12,
	A13, T13,
	A14, T14,
	A15, T15,
	HKT_T1, // HKT[T1]
	HKT_T2, // HKT[T2]
	HKT_T3, // HKT[T3]
	HKT_T4, // HKT[T4]
	HKT_T5, // HKT[T5]
	HKT_T6, // HKT[T6]
	HKT_T7, // HKT[T7]
	HKT_T8, // HKT[T8]
	HKT_T9, // HKT[T9]
	HKT_T10, // HKT[T10]
	HKT_T11, // HKT[T11]
	HKT_T12, // HKT[T12]
	HKT_T13, // HKT[T13]
	HKT_T14, // HKT[T14]
	HKT_T15, // HKT[T15]
	HKT_F_T2_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T2) func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T3_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T3) func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T4_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T4) func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T5_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T5) func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T6_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T6) func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T7_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T7) func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T8_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T8) func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T9_T10_T11_T12_T13_T14_T15, // HKT[func(T9) func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T10_T11_T12_T13_T14_T15, // HKT[func(T10) func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T11_T12_T13_T14_T15, // HKT[func(T11) func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T12_T13_T14_T15, // HKT[func(T12) func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T13_T14_T15, // HKT[func(T13) func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T14_T15, // HKT[func(T14) func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_F_T15, // HKT[func(T15) T.Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
	HKT_TUPLE15 any, // HKT[Tuple15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]]
](
	fmap MAP,
	fap1 AP1,
	fap2 AP2,
	fap3 AP3,
	fap4 AP4,
	fap5 AP5,
	fap6 AP6,
	fap7 AP7,
	fap8 AP8,
	fap9 AP9,
	fap10 AP10,
	fap11 AP11,
	fap12 AP12,
	fap13 AP13,
	fap14 AP14,
	f1 F1,
	f2 F2,
	f3 F3,
	f4 F4,
	f5 F5,
	f6 F6,
	f7 F7,
	f8 F8,
	f9 F9,
	f10 F10,
	f11 F11,
	f12 F12,
	f13 F13,
	f14 F14,
	f15 F15,
	t T.Tuple15[A1, A2, A3, A4, A5, A6, A7, A8, A9, A10, A11, A12, A13, A14, A15],
) HKT_TUPLE15 {
	return F.Pipe15(
		f1(t.F1),
		fmap(tupleConstructor15[T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]()),
		fap1(f2(t.F2)),
		fap2(f3(t.F3)),
		fap3(f4(t.F4)),
		fap4(f5(t.F5)),
		fap5(f6(t.F6)),
		fap6(f7(t.F7)),
		fap7(f8(t.F8)),
		fap8(f9(t.F9)),
		fap9(f10(t.F10)),
		fap10(f11(t.F11)),
		fap11(f12(t.F12)),
		fap12(f13(t.F13)),
		fap13(f14(t.F14)),
		fap14(f15(t.F15)),
	)
}
